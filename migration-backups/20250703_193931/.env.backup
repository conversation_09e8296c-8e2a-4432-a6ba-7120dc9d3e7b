# WebFlowMaster Environment Configuration
# IMPORTANT: Keep this file secure and never commit to version control

# Application Environment
NODE_ENV=development
PORT=5000

# Database Configuration - Updated for Supabase Migration
# Note: Resolved inconsistency - using port 5433 and database name 'webflowmaster' to match Docker setup
DATABASE_URL=postgresql://postgres:postgresql@localhost:5433/webflowmaster

# Supabase Configuration (Self-hosted instance)
# Update these values based on your self-hosted Supabase setup
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Database Connection for Supabase (Alternative configuration)
# Uncomment and update if your Supabase uses different settings
# SUPABASE_DB_URL=postgresql://postgres:postgresql@localhost:54322/postgres

# Security Configuration - Generated secure secrets
JWT_SECRET=kvjaJKVMl1BWWt8xQ9qdURRSMFm83nNwLX38GHombbI=
SESSION_SECRET=gYIb9f4bj+Ni/U6tVJjiacAhYu7vCKjkwpwSHz17I4A=

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=csv,xlsx,xls,json,xml,md

# Redis Configuration (Optional - for caching)
# REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=info

# Monitoring
ENABLE_METRICS=true
