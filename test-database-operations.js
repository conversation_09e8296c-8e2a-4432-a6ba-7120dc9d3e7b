#!/usr/bin/env node

/**
 * Database Operations Test Script
 * Tests CRUD operations to validate the migration
 */

import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { readFileSync } from 'fs';
import { eq } from 'drizzle-orm';

// Load environment variables from .env file
const envContent = readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});
Object.assign(process.env, envVars);

// Import schema (we'll define minimal schema here for testing)
const { pgTable, text, serial, integer, real, timestamp } = await import('drizzle-orm/pg-core');

const missions = pgTable("missions", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  courseNumber: integer("course_number").notNull(),
});

const transactions = pgTable("transactions", {
  id: serial("id").primaryKey(),
  date: text("date").notNull(),
  type: text("type").notNull(),
  amount: real("amount").notNull(),
  resultingBalance: real("resulting_balance").notNull(),
  missionId: integer("mission_id").references(() => missions.id),
  createdAt: timestamp("created_at").defaultNow(),
});

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('🔄 Testing database CRUD operations...');

async function testDatabaseOperations() {
  let sql;
  
  try {
    sql = postgres(DATABASE_URL, {
      max: 1,
      connect_timeout: 10,
    });
    
    const db = drizzle(sql);
    
    console.log('\n1️⃣ Testing READ operations...');
    
    // Test reading missions
    const allMissions = await db.select().from(missions);
    console.log(`✅ Found ${allMissions.length} missions`);
    
    if (allMissions.length > 0) {
      console.log('   Sample mission:', allMissions[0]);
    }
    
    // Test reading transactions
    const allTransactions = await db.select().from(transactions);
    console.log(`✅ Found ${allTransactions.length} transactions`);
    
    console.log('\n2️⃣ Testing CREATE operations...');
    
    // Create a test mission
    const testMission = await db.insert(missions).values({
      name: 'Test Migration Mission',
      courseNumber: 999
    }).returning();
    
    console.log('✅ Created test mission:', testMission[0]);
    
    // Create a test transaction
    const testTransaction = await db.insert(transactions).values({
      date: '2024-01',
      type: 'savings',
      amount: 100.50,
      resultingBalance: 1000.50,
      missionId: testMission[0].id
    }).returning();
    
    console.log('✅ Created test transaction:', testTransaction[0]);
    
    console.log('\n3️⃣ Testing UPDATE operations...');
    
    // Update the test mission
    const updatedMission = await db.update(missions)
      .set({ name: 'Updated Test Mission' })
      .where(eq(missions.id, testMission[0].id))
      .returning();
    
    console.log('✅ Updated test mission:', updatedMission[0]);
    
    console.log('\n4️⃣ Testing DELETE operations...');
    
    // Delete test transaction first (foreign key constraint)
    await db.delete(transactions).where(eq(transactions.id, testTransaction[0].id));
    console.log('✅ Deleted test transaction');
    
    // Delete test mission
    await db.delete(missions).where(eq(missions.id, testMission[0].id));
    console.log('✅ Deleted test mission');
    
    console.log('\n5️⃣ Testing complex queries...');
    
    // Test join query
    const missionsWithTransactions = await db
      .select({
        missionId: missions.id,
        missionName: missions.name,
        transactionCount: sql`COUNT(${transactions.id})::int`
      })
      .from(missions)
      .leftJoin(transactions, eq(missions.id, transactions.missionId))
      .groupBy(missions.id, missions.name)
      .limit(5);
    
    console.log('✅ Complex query successful');
    console.log('   Sample results:', missionsWithTransactions.slice(0, 2));
    
    console.log('\n🎉 All database operations completed successfully!');
    console.log('✅ Migration validation passed');
    
  } catch (error) {
    console.error('\n❌ Database operations test failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    process.exit(1);
  } finally {
    if (sql) {
      await sql.end();
    }
  }
}

// Run the test
testDatabaseOperations().catch(console.error);
