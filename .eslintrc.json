{
  "root": true,
  "env": {
    "browser": true,
    "es2022": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": ["./tsconfig.json", "./client/tsconfig.json"]
  },
  "plugins": [
    "@typescript-eslint",
    "import",
    "security"
  ],
  "rules": {
    // TypeScript specific rules
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-floating-promises": "error",
    "@typescript-eslint/no-misused-promises": "error",
    "@typescript-eslint/await-thenable": "error",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",
    "@typescript-eslint/no-unnecessary-type-assertion": "error",
    
    // General JavaScript/TypeScript rules
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "error",
    "no-alert": "error",
    "no-var": "error",
    "prefer-const": "error",
    "prefer-arrow-callback": "error",
    "arrow-spacing": "error",
    "no-duplicate-imports": "error",
    
    // Import rules
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "never",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "import/no-unresolved": "off",
    "import/no-cycle": "error",
    "import/no-self-import": "error",
    
    // Security rules
    "security/detect-object-injection": "warn",
    "security/detect-non-literal-regexp": "warn",
    "security/detect-unsafe-regex": "error",
    "security/detect-buffer-noassert": "error",
    "security/detect-child-process": "warn",
    "security/detect-disable-mustache-escape": "error",
    "security/detect-eval-with-expression": "error",
    "security/detect-no-csrf-before-method-override": "error",
    "security/detect-non-literal-fs-filename": "warn",
    "security/detect-non-literal-require": "warn",
    "security/detect-possible-timing-attacks": "warn",
    "security/detect-pseudoRandomBytes": "error",
    
    // Code quality rules
    "complexity": ["warn", 10],
    "max-depth": ["warn", 4],
    "max-lines": ["warn", 300],
    "max-lines-per-function": ["warn", 50],
    "max-params": ["warn", 4],
    "no-magic-numbers": ["warn", { "ignore": [0, 1, -1, 2] }],
    "no-nested-ternary": "error",
    "no-unneeded-ternary": "error",
    "prefer-template": "error",
    "yoda": "error"
  },
  "overrides": [
    {
      "files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
      "env": {
        "vitest/globals": true
      },
      "extends": ["plugin:vitest/recommended"],
      "plugins": ["vitest"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "no-magic-numbers": "off",
        "max-lines-per-function": "off"
      }
    },
    {
      "files": ["client/**/*.tsx", "client/**/*.ts"],
      "env": {
        "browser": true
      },
      "extends": [
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "plugin:jsx-a11y/recommended"
      ],
      "plugins": ["react", "react-hooks", "jsx-a11y"],
      "settings": {
        "react": {
          "version": "detect"
        }
      },
      "rules": {
        "react/react-in-jsx-scope": "off",
        "react/prop-types": "off",
        "react/display-name": "off",
        "react-hooks/rules-of-hooks": "error",
        "react-hooks/exhaustive-deps": "warn",
        "jsx-a11y/anchor-is-valid": "off"
      }
    },
    {
      "files": ["server/**/*.ts"],
      "env": {
        "node": true,
        "browser": false
      },
      "rules": {
        "no-console": "off"
      }
    },
    {
      "files": ["*.config.ts", "*.config.js", "vite.config.ts", "vitest.config.ts"],
      "rules": {
        "import/no-default-export": "off",
        "@typescript-eslint/no-var-requires": "off"
      }
    }
  ],
  "ignorePatterns": [
    "dist/",
    "build/",
    "coverage/",
    "node_modules/",
    "*.min.js",
    "public/",
    "client/dist/"
  ]
}
