#!/bin/bash

# PostgreSQL Connection Fix Script for Supabase
# Diagnoses and fixes PostgreSQL connectivity issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SUPABASE_DIR="/home/<USER>/GithubProjects/supabase"
DATABASE_NAME="db-finanzas"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test PostgreSQL on different ports
test_postgresql_ports() {
    log "Testing PostgreSQL on different ports..."
    
    local ports=(54322 5432 5433)
    local working_port=""
    
    for port in "${ports[@]}"; do
        log "Testing port $port..."
        if pg_isready -h localhost -p $port -U postgres &> /dev/null; then
            success "PostgreSQL is accessible on port $port"
            working_port=$port
            break
        else
            warning "PostgreSQL not accessible on port $port"
        fi
    done
    
    if [ -z "$working_port" ]; then
        error "PostgreSQL is not accessible on any standard port"
        return 1
    else
        echo "WORKING_PORT=$working_port"
        return 0
    fi
}

# Check if Supabase directory exists
check_supabase_directory() {
    log "Checking Supabase directory..."
    
    if [ -d "$SUPABASE_DIR" ]; then
        success "Supabase directory found at $SUPABASE_DIR"
        return 0
    else
        error "Supabase directory not found at $SUPABASE_DIR"
        echo ""
        echo "Please ensure you have Supabase installed at the correct location."
        echo "You can install it by:"
        echo "1. cd /home/<USER>/GithubProjects/"
        echo "2. git clone https://github.com/supabase/supabase.git"
        echo "3. cd supabase && supabase start"
        return 1
    fi
}

# Check Supabase status
check_supabase_status() {
    log "Checking Supabase status..."
    
    cd "$SUPABASE_DIR"
    
    # Try Supabase CLI
    if command -v supabase &> /dev/null; then
        log "Checking status with Supabase CLI..."
        if supabase status &> /dev/null; then
            success "Supabase is running (CLI)"
            supabase status
            return 0
        else
            warning "Supabase not running according to CLI"
        fi
    else
        warning "Supabase CLI not found"
    fi
    
    # Check Docker containers
    log "Checking Docker containers..."
    if docker ps | grep -q supabase; then
        success "Supabase Docker containers are running"
        docker ps | grep supabase
        return 0
    else
        warning "No Supabase Docker containers found"
        return 1
    fi
}

# Start Supabase
start_supabase() {
    log "Attempting to start Supabase..."
    
    cd "$SUPABASE_DIR"
    
    # Try Supabase CLI first
    if command -v supabase &> /dev/null; then
        log "Starting with Supabase CLI..."
        if supabase start; then
            success "Supabase started with CLI"
            return 0
        else
            warning "Failed to start with CLI"
        fi
    fi
    
    # Try Docker Compose
    if [ -f "docker-compose.yml" ]; then
        log "Starting with Docker Compose..."
        if docker-compose up -d; then
            success "Supabase started with Docker Compose"
            sleep 10  # Wait for services to be ready
            return 0
        else
            error "Failed to start with Docker Compose"
        fi
    elif [ -f "docker/docker-compose.yml" ]; then
        log "Starting with Docker Compose (docker directory)..."
        cd docker
        if docker-compose up -d; then
            success "Supabase started with Docker Compose"
            sleep 10  # Wait for services to be ready
            return 0
        else
            error "Failed to start with Docker Compose"
        fi
    fi
    
    error "Could not start Supabase"
    return 1
}

# Create database
create_database() {
    local port=$1
    
    log "Creating database '$DATABASE_NAME' on port $port..."
    
    # Check if database already exists
    if psql -h localhost -p $port -U postgres -lqt | cut -d \| -f 1 | grep -qw "$DATABASE_NAME"; then
        success "Database '$DATABASE_NAME' already exists"
        return 0
    fi
    
    # Create database
    if psql -h localhost -p $port -U postgres -c "CREATE DATABASE \"$DATABASE_NAME\";" &> /dev/null; then
        success "Database '$DATABASE_NAME' created"
        
        # Grant permissions
        psql -h localhost -p $port -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE \"$DATABASE_NAME\" TO postgres;" &> /dev/null
        success "Permissions granted"
        return 0
    else
        error "Failed to create database '$DATABASE_NAME'"
        return 1
    fi
}

# Update WebFlowMaster configuration
update_webflowmaster_config() {
    local port=$1
    
    log "Updating WebFlowMaster configuration for port $port..."
    
    # Backup current .env
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # Update DATABASE_URL
    sed -i "s|DATABASE_URL=postgresql://postgres:postgresql@localhost:[0-9]*/db-finanzas|DATABASE_URL=postgresql://postgres:postgresql@localhost:$port/db-finanzas|" .env
    
    success "WebFlowMaster configuration updated"
    
    # Show current configuration
    log "Current DATABASE_URL:"
    grep "DATABASE_URL" .env
}

# Test final connection
test_final_connection() {
    log "Testing final connection..."
    
    if node verify-db-finanzas-setup.js &> /dev/null; then
        success "WebFlowMaster can connect to db-finanzas database"
        return 0
    else
        error "WebFlowMaster still cannot connect"
        return 1
    fi
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              PostgreSQL Connection Fix Script               ║"
    echo "║                    for Supabase Integration                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # Step 1: Check if Supabase directory exists
    if ! check_supabase_directory; then
        exit 1
    fi
    
    # Step 2: Check Supabase status
    if ! check_supabase_status; then
        log "Supabase is not running. Attempting to start..."
        if ! start_supabase; then
            error "Could not start Supabase. Please start it manually."
            exit 1
        fi
    fi
    
    # Step 3: Test PostgreSQL ports
    log "Waiting for PostgreSQL to be ready..."
    sleep 5
    
    if port_result=$(test_postgresql_ports); then
        working_port=$(echo "$port_result" | grep "WORKING_PORT=" | cut -d'=' -f2)
        success "Found PostgreSQL running on port $working_port"
    else
        error "PostgreSQL is not accessible. Please check Supabase logs."
        exit 1
    fi
    
    # Step 4: Create database
    if ! create_database $working_port; then
        error "Could not create database. Please check permissions."
        exit 1
    fi
    
    # Step 5: Update WebFlowMaster configuration
    update_webflowmaster_config $working_port
    
    # Step 6: Test final connection
    if test_final_connection; then
        echo ""
        success "PostgreSQL connection fixed successfully!"
        echo ""
        echo -e "${BLUE}📋 Summary:${NC}"
        echo "   • PostgreSQL: Running on port $working_port"
        echo "   • Database: db-finanzas created and accessible"
        echo "   • WebFlowMaster: Configuration updated"
        echo "   • Connection: Working"
        echo ""
        echo -e "${YELLOW}📝 Next Steps:${NC}"
        echo "   1. Run database migration: npx drizzle-kit push"
        echo "   2. Test your application: npm run dev"
        echo "   3. Access Supabase Studio: http://localhost:54323"
    else
        error "Connection test failed. Please check the logs above."
        exit 1
    fi
}

# Run the script
main "$@"
