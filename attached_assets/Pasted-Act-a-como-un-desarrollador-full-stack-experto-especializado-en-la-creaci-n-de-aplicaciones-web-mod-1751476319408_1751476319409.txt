Actúa como un desarrollador full-stack experto, especializado en la creación de aplicaciones web modernas y responsive. Tu tarea es generar el código completo para una aplicación de seguimiento financiero personal basada en una hoja de cálculo CSV proporcionada. La aplicación debe ser intuitiva, funcional, replicar la lógica de negocio del archivo original y funcionar perfectamente en cualquier navegador, desde un móvil hasta un escritorio.

**1. Contexto y Datos de Origen**

La aplicación se basará en el siguiente análisis financiero, que rastrea ahorros, descuentos y un saldo acumulado a lo largo del tiempo, agrupado en "Misiones". También detalla gastos específicos en diferentes categorías.

--- INICIO DE DATOS CSV ---
Curso,Mes/Año,Ahorros,Descuento,Saldo Cuba,50%,En Banco,1ra,Total x Gastos,2da,Total x Gastos,3ra,Total x Gastos
4,TOTAL,$15366.00,$8332.46,$7033.54,$3516.77,,$2440.00,$1070.00,$2459.33,$1819.33,$3384.30,$384.30
1,10/2021,,$10.00,-$10.00,,,,,,,,
,11/2021,$480.00,,$470.00,,,,,,,,
,12/2021,$331.00,,$801.00,,,,,,,,
,1/2022,$321.00,,$1122.00,,,,,,,,
,2/2022,$331.00,,$1453.00,,,,,,,,
,3/2022,$331.00,,$1784.00,,,,,,,,
,4/2022,$331.00,,$2115.00,,,,,,,,
,5/2022,$331.00,,$2446.00,,,,,,,,
,6/2022,$331.00,,$2777.00,,,,,,,,
,7/2022,$331.00,,$3108.00,,,,,,,,
,8/2022,$331.00,,$3439.00,,,,,,,,
2,9/2022,$371.00,,$3810.00,,,,,,,,
,10/2022,$381.00,,$4191.00,,,,,,,,
,11/2022,$381.00,,$4572.00,,,,,,,,
,12/2022,$310.00,,$4882.00,,,,,,,,
,12/2022,,$2440.00,$2442.00,,,,,,,,
,2/2023,$308.00,,$2749.00,,,,,,,,
,3/2023,$331.00,,$3071.67,,,,,,,,
,4/2023,$331.00,,$3402.67,,,,,,,,
,5/2023,$321.00,,$3723.67,,,,,,,,
,6/2023,$431.00,,$4154.67,,,,,,,,
,7/2023,$431.00,,$4585.67,,,,,,,,
,8/2023,$335.00,,$4920.67,,,,,,,,
,9/2023,,$2459.33,$2461.34,,,,,,,,
3,10/2023,,$13.75,$2446.59,,,,,,,,
,11/2023,$455.00,,$2901.59,,,,,,,,
,12/2023,$431.00,,$3332.59,,,,,,,,
,1/2024,$421.00,,$3753.59,,,,,,,,
,2/2024,$431.00,,$4184.59,,,,,,,,
,3/2024,$431.00,,$4615.59,,,,,,,,
,4/2024,$431.00,,$5046.59,,,,,,,,
,5/2024,$431.00,,$5477.59,,,,,,,,
,6/2024,$431.00,,$5908.59,,,,,,,,
,7/2024,$431.00,,$6339.59,,,,,,,,
,8/2024,$431.00,,$6770.59,,,,,,,,
,8/2024,,$3384.30,$3386.29,,,,,,,,
4,9/2024,$385.00,,$3770.29,,,,,,,,
,10/2024,,$13.75,$3756.54,,,,,,,,
,11/2024,$308.00,,$4064.54,,,,,,,,
,12/2024,$331.00,,$4395.54,,,,,,,,
,1/2025,$321.00,,$4716.54,,,,,,,,
,2/2025,$331.00,,$5047.54,,,,,,,,
,3/2025,$331.00,,$5378.54,,,,,,,,
,4/2025,$331.00,,$5709.54,,,,,,,,
,5/2025,$331.00,,$6040.54,,,,,,,,
,6/2025,$331.00,,$6371.54,,,,,,,,
,7/2025,$331.00,,$6702.54,,,,,,,,
,8/2025,$331.00,,$7033.54,,,,,,,,
--- FIN DE DATOS CSV ---
--- INICIO DE DATOS DE GASTOS ---
# Misión 1:
# Categoría "1ra":
- Laptop HP Elitebook: $600.00
- Laptop HP Elitebook: $570.00
- Disco Duro: $100.00
- Transferencia USD: $100.00
# Categoría "2da":
- Extraccion: $418.00
- Transferencia: $114.00
- Latas de pintura: $58.00
- Cajita decodificadora: $50.00
# Categoría "3ra":
- Motos: $3000.00
--- FIN DE DATOS DE GASTOS ---

**2. Concepto de la Aplicación y Lógica de Negocio**

La aplicación es un "Seguimiento de Misiones Financieras".

*   **Misiones (`Curso`):** Son períodos de tiempo distintos (Misión 1, Misión 2, etc.). La app debe permitir ver el progreso de la misión actual y revisar las pasadas.
*   **Saldo Principal (`Saldo Cuba`):** Este es el corazón de la app. Es un saldo acumulativo. La fórmula es: `SaldoAnterior + AhorrosDelMes - DescuentosDelMes`.
*   **Ahorros:** Entradas de dinero mensuales.
*   **Descuentos:** Salidas de dinero. A menudo son grandes sumas que se retiran para un propósito específico (por ejemplo, los gastos de vacaciones).
*   **Saldo al 50%:** Un valor calculado que siempre es `Saldo Cuba / 2`. Es una métrica de referencia importante.
*   **Gastos por Categoría (`1ra`, `2da`, `3ra`):** Estos representan los gastos detallados que se realizan después de un "Descuento" importante. Por ejemplo, el "Descuento" de `$2440.00` financia los gastos de la categoría "1ra". La app debe permitir registrar estos gastos itemizados.

**3. Especificaciones Técnicas**

*   **Stack Tecnológico:**
    *   **Frontend:** React (usando Vite.js) y Tailwind CSS para un diseño limpio y responsive.
    *   **Backend:** Node.js con Express.js para crear una API RESTful.
    *   **Base de Datos:** SQLite para simplicidad y portabilidad.
*   **Estructura de Archivos:** Organiza el código en una estructura clara `client`/`server`.

**4. Diseño de la Interfaz de Usuario (UI) - Responsive**

Crea una interfaz limpia y moderna. El diseño debe ser "mobile-first" por defecto, pero debe adaptarse y aprovechar el espacio en pantallas más grandes (tablets y escritorio), por ejemplo, usando un layout de columnas o un menú lateral en la versión de escritorio.

*   **Pantalla Principal (Dashboard):**
    *   Mostrar prominentemente el **Saldo Cuba actual** y el **Saldo al 50%** en tarjetas de gran tamaño.
    *   Indicar la **Misión actual** (ej. "Misión 4").
    *   Un gráfico de líneas que muestre la evolución del `Saldo Cuba` en los últimos 12 meses.
    *   Un botón flotante `(+)` en la vista móvil, o un botón claramente visible en la vista de escritorio, para añadir un nuevo registro (Ahorro o Descuento).

*   **Pantalla de Movimientos (Historial):**
    *   Una tabla o lista cronológica de todos los movimientos (Ahorros y Descuentos).
    *   Cada fila debe mostrar: `Mes/Año`, tipo de movimiento (con íconos visuales), el monto, y el `Saldo Cuba` resultante.
    *   Un menú desplegable para filtrar los movimientos por Misión.

*   **Pantalla de Gastos Detallados ("Vacaciones"):**
    *   En escritorio, esta sección podría ser una parte del Dashboard principal. En móvil, una pantalla separada.
    *   Usar pestañas (Tabs) para cada categoría de gastos: "1ra", "2da", "3ra".
    *   Dentro de cada pestaña, mostrar una lista de los gastos itemizados con su descripción y monto.
    *   Mostrar el **Total Gastado** en esa categoría y compararlo con el "Descuento" que lo financió.
    *   Un botón para añadir un nuevo gasto a la categoría seleccionada.

*   **Formularios (Implementados como Modales):**
    *   **Añadir Movimiento:** Campos para: Fecha (mes/año), Tipo (Ahorro/Descuento), y Monto.
    *   **Añadir Gasto:** Campos para: Descripción, Monto y Categoría ("1ra", "2da", "3ra").

**5. Modelo de Datos (Esquema de la Base de Datos SQLite)**

Define y crea las siguientes tablas:

*   `Misiones`: `id` (PK), `nombre` (TEXT).
*   `Movimientos`: `id` (PK), `fecha` (TEXT 'YYYY-MM'), `tipo` (TEXT 'ahorro'/'descuento'), `monto` (REAL), `mision_id` (FK), `saldo_resultante` (REAL).
*   `CategoriasGasto`: `id` (PK), `nombre` (TEXT), `mision_id` (FK).
*   `Gastos`: `id` (PK), `descripcion` (TEXT), `monto` (REAL), `categoria_id` (FK).

**6. Instrucciones de Implementación**

1.  **Genera la estructura de directorios** para `client` y `server`.
2.  **Crea el servidor Express** con endpoints API RESTful para operaciones CRUD (Crear, Leer, Actualizar, Borrar) sobre Misiones, Movimientos y Gastos.
3.  **Crea la base de datos SQLite** y un script (`database.js` en el backend) que:
    *   Cree las tablas si no existen.
    *   **Crucial:** Pobla las tablas con los datos iniciales extraídos del CSV y de la lista de gastos. Interpreta los datos del CSV para crear los registros correctos en las tablas `Misiones` y `Movimientos`.
4.  **Desarrolla la aplicación React** con componentes para cada pantalla y los modales. Usa hooks de React (`useState`, `useEffect`) para manejar el estado y la obtención de datos.
5.  **Implementa la lógica de cálculo** dinámicamente. El `Saldo Cuba` y `Saldo al 50%` deben recalcularse y actualizarse en la UI cada vez que se añade un nuevo movimiento.
6.  **Utiliza Tailwind CSS** para estilizar todos los componentes, asegurando que el diseño sea responsive usando las utilidades de `sm:`, `md:`, `lg:`.
7.  **Proporciona un archivo `README.md`** con instrucciones claras sobre cómo instalar dependencias (`npm install`) y ejecutar la aplicación completa (`npm run dev`).

Tu objetivo final es entregar un prototipo de aplicación web completo y funcional que digitalice la experiencia de la hoja de cálculo, haciéndola interactiva y accesible desde cualquier navegador moderno.