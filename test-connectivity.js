#!/usr/bin/env node

/**
 * Database Connectivity Test Script
 * Tests connection to the database and validates basic operations
 */

import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { readFileSync } from 'fs';

// Load environment variables from .env file
const envContent = readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});
Object.assign(process.env, envVars);

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('🔄 Testing database connectivity with db-finanzas...');
console.log(`📍 Database URL: ${DATABASE_URL.replace(/:[^:@]*@/, ':****@')}`);

async function testConnectivity() {
  let sql;
  
  try {
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing basic connection...');
    sql = postgres(DATABASE_URL, {
      max: 1,
      connect_timeout: 10,
    });
    
    const result = await sql`SELECT version()`;
    console.log('✅ Connection successful');
    console.log(`📊 PostgreSQL version: ${result[0].version.split(' ')[0]} ${result[0].version.split(' ')[1]}`);
    
    // Test 2: Database existence and permissions
    console.log('\n2️⃣ Testing database permissions...');
    const dbInfo = await sql`
      SELECT 
        current_database() as database_name,
        current_user as user_name,
        session_user as session_user
    `;
    console.log('✅ Database permissions verified');
    console.log(`📊 Database: ${dbInfo[0].database_name}`);
    console.log(`👤 User: ${dbInfo[0].user_name}`);
    
    // Test 3: Schema check
    console.log('\n3️⃣ Checking existing tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    if (tables.length > 0) {
      console.log('✅ Found existing tables:');
      tables.forEach(table => console.log(`   📋 ${table.table_name}`));
    } else {
      console.log('ℹ️  No tables found (fresh database)');
    }
    
    // Test 4: Drizzle ORM connection
    console.log('\n4️⃣ Testing Drizzle ORM connection...');
    const db = drizzle(sql);

    // Try to query a simple table check
    try {
      await sql`SELECT 1 as test`;
      console.log('✅ Drizzle ORM connection working');
    } catch (error) {
      console.log(`⚠️  Drizzle ORM test failed: ${error.message}`);
    }
    
    // Test 5: Write permissions
    console.log('\n5️⃣ Testing write permissions...');
    try {
      await sql`CREATE TABLE IF NOT EXISTS connectivity_test (id SERIAL PRIMARY KEY, test_time TIMESTAMP DEFAULT NOW())`;
      await sql`INSERT INTO connectivity_test DEFAULT VALUES`;
      const testResult = await sql`SELECT COUNT(*) as count FROM connectivity_test`;
      await sql`DROP TABLE connectivity_test`;
      console.log('✅ Write permissions verified');
    } catch (error) {
      console.log(`❌ Write permission test failed: ${error.message}`);
    }
    
    console.log('\n🎉 All connectivity tests completed successfully!');
    console.log('✅ Database is ready for migration');
    
  } catch (error) {
    console.error('\n❌ Connectivity test failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   - Check if PostgreSQL/Supabase is running');
      console.error('   - Verify the host and port in DATABASE_URL');
      console.error('   - Check firewall settings');
    } else if (error.code === 'ENOTFOUND') {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   - Check the hostname in DATABASE_URL');
      console.error('   - Verify DNS resolution');
    } else if (error.message.includes('authentication')) {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   - Check username and password in DATABASE_URL');
      console.error('   - Verify user permissions in PostgreSQL');
    }
    
    process.exit(1);
  } finally {
    if (sql) {
      await sql.end();
    }
  }
}

// Run the test
testConnectivity().catch(console.error);
