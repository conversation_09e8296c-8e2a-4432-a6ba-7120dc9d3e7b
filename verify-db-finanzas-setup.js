#!/usr/bin/env node

/**
 * Comprehensive Verification Script for db-finanzas Setup
 * Tests all aspects of the WebFlowMaster → Supabase → db-finanzas configuration
 */

import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { readFileSync } from 'fs';

// Load environment variables from .env file
const envContent = readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});
Object.assign(process.env, envVars);

const DATABASE_URL = process.env.DATABASE_URL;
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;

console.log('🔄 Comprehensive db-finanzas Setup Verification');
console.log('=' .repeat(60));

async function verifySetup() {
  let sql;
  let testsPassed = 0;
  let totalTests = 0;
  
  try {
    // Test 1: Environment Variables
    console.log('\n1️⃣ Testing Environment Variables...');
    totalTests++;
    
    if (!DATABASE_URL) {
      throw new Error('DATABASE_URL not set');
    }
    
    if (!DATABASE_URL.includes('db-finanzas')) {
      throw new Error('DATABASE_URL does not contain "db-finanzas"');
    }
    
    console.log(`✅ DATABASE_URL: ${DATABASE_URL.replace(/:[^:@]*@/, ':****@')}`);
    console.log(`✅ SUPABASE_URL: ${SUPABASE_URL || 'Not set'}`);
    console.log(`✅ SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY ? 'Set' : 'Not set'}`);
    testsPassed++;
    
    // Test 2: Database Connection
    console.log('\n2️⃣ Testing Database Connection...');
    totalTests++;
    
    sql = postgres(DATABASE_URL, {
      max: 1,
      connect_timeout: 10,
    });
    
    const versionResult = await sql`SELECT version()`;
    console.log(`✅ PostgreSQL Version: ${versionResult[0].version.split(' ')[0]} ${versionResult[0].version.split(' ')[1]}`);
    
    const dbInfo = await sql`
      SELECT 
        current_database() as database_name,
        current_user as user_name
    `;
    
    if (dbInfo[0].database_name !== 'db-finanzas') {
      throw new Error(`Expected database 'db-finanzas', got '${dbInfo[0].database_name}'`);
    }
    
    console.log(`✅ Connected to database: ${dbInfo[0].database_name}`);
    console.log(`✅ User: ${dbInfo[0].user_name}`);
    testsPassed++;
    
    // Test 3: Schema Verification
    console.log('\n3️⃣ Testing Database Schema...');
    totalTests++;
    
    const tables = await sql`
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns 
              WHERE table_name = t.table_name AND table_schema = 'public') as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    const expectedTables = [
      'employees', 'expense_categories', 'expenses', 'missions',
      'payroll_concepts', 'payroll_entries', 'payroll_reports', 'transactions'
    ];
    
    console.log(`✅ Found ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`   📋 ${table.table_name} (${table.column_count} columns)`);
    });
    
    const missingTables = expectedTables.filter(expected => 
      !tables.some(table => table.table_name === expected)
    );
    
    if (missingTables.length > 0) {
      console.log(`⚠️  Missing tables: ${missingTables.join(', ')}`);
      console.log('   This is normal if you haven\'t run migrations yet');
    } else {
      console.log('✅ All expected tables found');
    }
    testsPassed++;
    
    // Test 4: Drizzle ORM Integration
    console.log('\n4️⃣ Testing Drizzle ORM Integration...');
    totalTests++;
    
    const db = drizzle(sql);
    
    // Test a simple query
    const simpleTest = await sql`SELECT 1 as test_value`;
    if (simpleTest[0].test_value !== 1) {
      throw new Error('Simple query test failed');
    }
    
    console.log('✅ Drizzle ORM connection working');
    console.log('✅ Basic queries functional');
    testsPassed++;
    
    // Test 5: Write Permissions
    console.log('\n5️⃣ Testing Write Permissions...');
    totalTests++;
    
    const testTableName = 'webflowmaster_test_' + Date.now();
    
    try {
      await sql`CREATE TABLE ${sql(testTableName)} (id SERIAL PRIMARY KEY, test_data TEXT)`;
      await sql`INSERT INTO ${sql(testTableName)} (test_data) VALUES ('test')`;
      const testResult = await sql`SELECT COUNT(*) as count FROM ${sql(testTableName)}`;
      await sql`DROP TABLE ${sql(testTableName)}`;
      
      if (testResult[0].count !== '1') {
        throw new Error('Write test verification failed');
      }
      
      console.log('✅ Create table permissions verified');
      console.log('✅ Insert permissions verified');
      console.log('✅ Drop table permissions verified');
    } catch (error) {
      throw new Error(`Write permissions test failed: ${error.message}`);
    }
    testsPassed++;
    
    // Test 6: Supabase API (if configured)
    console.log('\n6️⃣ Testing Supabase API...');
    totalTests++;
    
    if (SUPABASE_URL) {
      try {
        const response = await fetch(`${SUPABASE_URL}/health`);
        if (response.ok) {
          console.log('✅ Supabase API is responding');
        } else {
          console.log('⚠️  Supabase API responded with non-OK status');
        }
      } catch (error) {
        console.log('⚠️  Supabase API not accessible (this is optional)');
      }
    } else {
      console.log('⚠️  SUPABASE_URL not configured (this is optional)');
    }
    testsPassed++;
    
    // Test 7: Performance Test
    console.log('\n7️⃣ Testing Database Performance...');
    totalTests++;
    
    const startTime = Date.now();
    for (let i = 0; i < 10; i++) {
      await sql`SELECT 1`;
    }
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / 10;
    
    console.log(`✅ Average query time: ${avgTime.toFixed(2)}ms`);
    
    if (avgTime > 100) {
      console.log('⚠️  Query performance seems slow (>100ms average)');
    } else {
      console.log('✅ Query performance is good');
    }
    testsPassed++;
    
    // Final Summary
    console.log('\n' + '=' .repeat(60));
    console.log('📊 VERIFICATION SUMMARY');
    console.log('=' .repeat(60));
    
    console.log(`✅ Tests Passed: ${testsPassed}/${totalTests}`);
    
    if (testsPassed === totalTests) {
      console.log('🎉 ALL TESTS PASSED!');
      console.log('✅ WebFlowMaster is successfully configured for db-finanzas');
      console.log('✅ Database connectivity is working');
      console.log('✅ All permissions are properly set');
      console.log('✅ Ready for production use');
    } else {
      console.log('⚠️  Some tests failed or had warnings');
      console.log('   Please review the output above for details');
    }
    
    console.log('\n📝 Next Steps:');
    console.log('   1. If tables are missing, run: npx drizzle-kit push');
    console.log('   2. Start your application: npm run dev');
    console.log('   3. Test all application features');
    console.log('   4. Deploy to production when ready');
    
    console.log('\n🔗 Useful Commands:');
    console.log('   • Test connectivity: node test-connectivity.js');
    console.log('   • Run migrations: npx drizzle-kit push');
    console.log('   • Start app: npm run dev');
    console.log('   • Access Supabase Studio: http://localhost:54323');
    
  } catch (error) {
    console.error('\n❌ Verification failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Troubleshooting:');
      console.error('   - Ensure Supabase is running');
      console.error('   - Check if PostgreSQL is accessible on the configured port');
      console.error('   - Run: ./configure-supabase.sh');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('\n💡 Troubleshooting:');
      console.error('   - Database "db-finanzas" may not exist');
      console.error('   - Run: ./configure-supabase.sh to create it');
      console.error('   - Or run: ./migrate-to-db-finanzas.sh to migrate data');
    }
    
    process.exit(1);
  } finally {
    if (sql) {
      await sql.end();
    }
  }
}

// Run verification
verifySetup().catch(console.error);
