#!/usr/bin/env node

/**
 * Test Direct Database Connection
 * Tests connection using different methods to find the working one
 */

import postgres from 'postgres';
import { readFileSync } from 'fs';

// Load environment variables
const envContent = readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});
Object.assign(process.env, envVars);

console.log('🔄 Testing Direct Database Connection Methods');
console.log('=' .repeat(60));

async function testConnections() {
  const connectionMethods = [
    {
      name: 'Current .env configuration',
      url: process.env.DATABASE_URL
    },
    {
      name: 'Docker container direct (postgres password)',
      url: '***********************************************/db-finanzas'
    },
    {
      name: 'Localhost with postgres password',
      url: 'postgresql://postgres:postgres@localhost:5432/db-finanzas'
    },
    {
      name: 'Localhost port 54322 with postgres password',
      url: 'postgresql://postgres:postgres@localhost:54322/db-finanzas'
    },
    {
      name: 'Default postgres database',
      url: 'postgresql://postgres:postgres@localhost:5432/postgres'
    }
  ];

  for (const method of connectionMethods) {
    console.log(`\n🧪 Testing: ${method.name}`);
    console.log(`   URL: ${method.url.replace(/:[^:@]*@/, ':****@')}`);
    
    let sql;
    try {
      sql = postgres(method.url, {
        max: 1,
        connect_timeout: 5,
      });
      
      // Test basic connection
      const result = await sql`SELECT current_database(), current_user, version()`;
      
      console.log(`   ✅ Connection successful!`);
      console.log(`   📊 Database: ${result[0].current_database}`);
      console.log(`   👤 User: ${result[0].current_user}`);
      console.log(`   🗄️ PostgreSQL: ${result[0].version.split(' ')[1]}`);
      
      // Test if db-finanzas exists
      try {
        const dbCheck = await sql`SELECT 1 FROM pg_database WHERE datname = 'db-finanzas'`;
        if (dbCheck.length > 0) {
          console.log(`   ✅ Database 'db-finanzas' exists`);
        } else {
          console.log(`   ⚠️  Database 'db-finanzas' does not exist`);
        }
      } catch (error) {
        console.log(`   ⚠️  Could not check db-finanzas existence: ${error.message}`);
      }
      
      // If this connection works, update the .env file
      if (method.url !== process.env.DATABASE_URL) {
        console.log(`   💡 This connection works! Consider updating .env file.`);
        console.log(`   📝 Suggested DATABASE_URL: ${method.url}`);
      }
      
      await sql.end();
      
      // If we found a working connection, try to connect to db-finanzas specifically
      if (method.url.includes('postgres') && !method.url.includes('db-finanzas')) {
        const dbFinanzasUrl = method.url.replace('/postgres', '/db-finanzas');
        console.log(`\n   🔄 Testing connection to db-finanzas specifically...`);
        
        try {
          const sqlDbFinanzas = postgres(dbFinanzasUrl, {
            max: 1,
            connect_timeout: 5,
          });
          
          const dbResult = await sqlDbFinanzas`SELECT current_database()`;
          console.log(`   ✅ Successfully connected to db-finanzas!`);
          console.log(`   📊 Connected to: ${dbResult[0].current_database}`);
          
          await sqlDbFinanzas.end();
          
          console.log(`\n🎉 WORKING CONNECTION FOUND!`);
          console.log(`📝 Update your .env file with:`);
          console.log(`DATABASE_URL=${dbFinanzasUrl}`);
          
          return dbFinanzasUrl;
          
        } catch (error) {
          console.log(`   ❌ Could not connect to db-finanzas: ${error.message}`);
        }
      }
      
      return method.url;
      
    } catch (error) {
      console.log(`   ❌ Connection failed: ${error.message}`);
      if (sql) {
        try {
          await sql.end();
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    }
  }
  
  console.log(`\n❌ No working connection found.`);
  console.log(`\n💡 Troubleshooting suggestions:`);
  console.log(`   1. Ensure Supabase containers are running: docker ps | grep supabase`);
  console.log(`   2. Check if PostgreSQL is accessible: docker exec supabase-db psql -U postgres -c "SELECT 1"`);
  console.log(`   3. Verify the database exists: docker exec supabase-db psql -U postgres -c "\\l"`);
  console.log(`   4. Check Supabase logs: docker logs supabase-db`);
  
  return null;
}

// Run the test
testConnections().then(workingUrl => {
  if (workingUrl) {
    console.log(`\n✅ Test completed successfully!`);
    console.log(`🔗 Working connection: ${workingUrl.replace(/:[^:@]*@/, ':****@')}`);
  } else {
    console.log(`\n❌ No working connection found.`);
    process.exit(1);
  }
}).catch(console.error);
