#!/bin/bash

# WebFlowMaster Neon to Supabase Migration - Backup Script
# This script creates backups of critical files before migration

echo "🔄 Creating migration backups..."

# Create backup directory with timestamp
BACKUP_DIR="migration-backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup critical configuration files
echo "📁 Backing up configuration files..."
cp .env "$BACKUP_DIR/.env.backup" 2>/dev/null || echo "⚠️  .env file not found"
cp package.json "$BACKUP_DIR/package.json.backup"
cp package-lock.json "$BACKUP_DIR/package-lock.json.backup" 2>/dev/null || echo "⚠️  package-lock.json not found"
cp drizzle.config.ts "$BACKUP_DIR/drizzle.config.ts.backup"

# Backup database connection files
echo "🗄️  Backing up database files..."
cp server/db.ts "$BACKUP_DIR/db.ts.backup"
cp server/config/index.ts "$BACKUP_DIR/config.index.ts.backup" 2>/dev/null || echo "⚠️  config/index.ts not found"

# Create migration log
echo "📝 Creating migration log..."
cat > "$BACKUP_DIR/migration-log.txt" << EOF
WebFlowMaster Neon to Supabase Migration
Backup created: $(date)
Original configuration:
- Node.js project with Neon database
- Drizzle ORM with neon-serverless adapter
- Express.js backend with custom JWT auth

Migration target:
- Self-hosted Supabase instance
- Standard PostgreSQL connection
- Maintained Drizzle ORM with postgres-js adapter

Files backed up:
- .env (environment configuration)
- package.json (dependencies)
- package-lock.json (dependency lock)
- drizzle.config.ts (ORM configuration)
- server/db.ts (database connection)
- server/config/index.ts (application configuration)

Backup location: $BACKUP_DIR
EOF

echo "✅ Backup completed successfully!"
echo "📍 Backup location: $BACKUP_DIR"
echo ""
echo "To restore from backup if needed:"
echo "  cp $BACKUP_DIR/.env.backup .env"
echo "  cp $BACKUP_DIR/package.json.backup package.json"
echo "  cp $BACKUP_DIR/package-lock.json.backup package-lock.json"
echo "  cp $BACKUP_DIR/drizzle.config.ts.backup drizzle.config.ts"
echo "  cp $BACKUP_DIR/db.ts.backup server/db.ts"
echo "  npm install"
