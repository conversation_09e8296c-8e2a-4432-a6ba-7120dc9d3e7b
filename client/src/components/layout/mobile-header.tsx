import { <PERSON><PERSON> } from "@/components/ui/button";
import { DollarSign, MoreVertical } from "lucide-react";

export default function MobileHeader() {
  return (
    <header className="lg:hidden bg-white shadow-sm border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <DollarSign className="w-5 h-5 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">Misiones Financieras</h1>
            <p className="text-sm text-gray-500">Misión 4</p>
          </div>
        </div>
        <Button variant="ghost" size="icon">
          <MoreVertical className="w-6 h-6" />
        </Button>
      </div>
    </header>
  );
}
