import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Plus, DollarSign, TrendingUp, PiggyBank, ArrowUp, ArrowDown } from "lucide-react";
import BalanceChart from "@/components/charts/balance-chart";
import TransactionModal from "@/components/modals/transaction-modal";
import { formatCurrency, formatDate } from "@/lib/utils";
import type { Mission, TransactionWithMission } from "@shared/schema";

export default function Dashboard() {
  const [selectedMissionId, setSelectedMissionId] = useState<number>(4);
  const [transactionModalOpen, setTransactionModalOpen] = useState(false);

  const { data: missions, isLoading: missionsLoading } = useQuery<Mission[]>({
    queryKey: ["/api/missions"],
  });

  const { data: analytics, isLoading: analyticsLoading } = useQuery<{
    currentBalance: number;
    totalSavings: number;
    halfBalance: number;
  }>({
    queryKey: ["/api/analytics/balance"],
  });

  const { data: balanceHistory, isLoading: historyLoading } = useQuery<{
    date: string;
    balance: number;
  }[]>({
    queryKey: ["/api/analytics/balance-history", { months: 12 }],
  });

  const { data: recentTransactions, isLoading: transactionsLoading } = useQuery<TransactionWithMission[]>({
    queryKey: ["/api/transactions"],
  });

  const currentMission = missions?.find(m => m.id === selectedMissionId);

  if (missionsLoading || analyticsLoading) {
    return (
      <div className="p-4 lg:p-8">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
          <Skeleton className="h-80" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
            <p className="text-gray-600 mt-1">Resumen de tu progreso financiero</p>
          </div>
          <div className="flex items-center space-x-3">
            <Select value={selectedMissionId.toString()} onValueChange={(value) => setSelectedMissionId(parseInt(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {missions?.map((mission) => (
                  <SelectItem key={mission.id} value={mission.id.toString()}>
                    {mission.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={() => setTransactionModalOpen(true)}
              className="hidden lg:flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Movimiento
            </Button>
          </div>
        </div>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card className="balance-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                  Saldo Cuba
                </p>
                <p className="text-3xl font-bold text-gray-900 font-mono mt-2">
                  {analytics ? formatCurrency(analytics.currentBalance) : "--"}
                </p>
              </div>
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-green-600 text-sm font-medium">+2.3%</span>
              <span className="text-gray-500 text-sm ml-2">vs mes anterior</span>
            </div>
          </CardContent>
        </Card>

        <Card className="balance-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                  Saldo 50%
                </p>
                <p className="text-3xl font-bold text-gray-900 font-mono mt-2">
                  {analytics ? formatCurrency(analytics.halfBalance) : "--"}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: "50%" }}></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="balance-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                  Total Ahorros
                </p>
                <p className="text-3xl font-bold text-gray-900 font-mono mt-2">
                  {analytics ? formatCurrency(analytics.totalSavings) : "--"}
                </p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                <PiggyBank className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-green-600 text-sm font-medium">+$331</span>
              <span className="text-gray-500 text-sm ml-2">este mes</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chart Section */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Evolución del Saldo</CardTitle>
              <p className="text-gray-600 text-sm">Últimos 12 meses</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="secondary" size="sm">12M</Button>
              <Button variant="ghost" size="sm">6M</Button>
              <Button variant="ghost" size="sm">3M</Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {historyLoading ? (
            <Skeleton className="h-[300px]" />
          ) : balanceHistory ? (
            <BalanceChart data={balanceHistory} />
          ) : (
            <div className="h-[300px] flex items-center justify-center text-muted-foreground">
              No hay datos disponibles
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Movimientos Recientes</CardTitle>
            <Button variant="ghost" size="sm">Ver todos</Button>
          </div>
        </CardHeader>
        <CardContent>
          {transactionsLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-16" />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {recentTransactions?.slice(0, 5).map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      transaction.type === 'savings' 
                        ? 'bg-green-100' 
                        : 'bg-yellow-100'
                    }`}>
                      {transaction.type === 'savings' ? (
                        <ArrowUp className="w-5 h-5 text-green-600" />
                      ) : (
                        <ArrowDown className="w-5 h-5 text-yellow-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {transaction.type === 'savings' ? 'Ahorro Mensual' : 'Descuento'}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatDate(transaction.date)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold font-mono ${
                      transaction.type === 'savings' 
                        ? 'text-green-600' 
                        : 'text-yellow-600'
                    }`}>
                      {transaction.type === 'savings' ? '+' : '-'}
                      {formatCurrency(transaction.amount)}
                    </p>
                    <p className="text-sm text-gray-500 font-mono">
                      {formatCurrency(transaction.resultingBalance)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Floating Action Button (Mobile) */}
      <Button 
        onClick={() => setTransactionModalOpen(true)}
        className="lg:hidden fixed bottom-20 right-4 w-14 h-14 rounded-full shadow-lg"
        size="icon"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <TransactionModal 
        open={transactionModalOpen} 
        onOpenChange={setTransactionModalOpen} 
        currentMissionId={selectedMissionId}
      />
    </div>
  );
}
