import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Plus, Trash2, Monitor, DollarSign } from "lucide-react";
import ExpenseModal from "@/components/modals/expense-modal";
import { formatCurrency } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { ExpenseCategory, ExpenseWithCategory } from "@shared/schema";

export default function Expenses() {
  const [activeTab, setActiveTab] = useState("1");
  const [expenseModalOpen, setExpenseModalOpen] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number>();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: categories, isLoading: categoriesLoading } = useQuery<ExpenseCategory[]>({
    queryKey: ["/api/expense-categories"],
  });

  const { data: expenses, isLoading: expensesLoading } = useQuery<ExpenseWithCategory[]>({
    queryKey: ["/api/expenses"],
  });

  const deleteExpense = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/expenses/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/expenses"] });
      toast({
        title: "Gasto eliminado",
        description: "El gasto se ha eliminado correctamente.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "No se pudo eliminar el gasto. Intenta de nuevo.",
        variant: "destructive",
      });
      console.error("Error deleting expense:", error);
    },
  });

  const handleDeleteExpense = (id: number) => {
    if (confirm("¿Estás seguro de que quieres eliminar este gasto?")) {
      deleteExpense.mutate(id);
    }
  };

  const handleAddExpense = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    setExpenseModalOpen(true);
  };

  // Group expenses by category
  const expensesByCategory = expenses?.reduce((acc: Record<string, ExpenseWithCategory[]>, expense) => {
    const categoryName = expense.category?.name || 'unknown';
    if (!acc[categoryName]) {
      acc[categoryName] = [];
    }
    acc[categoryName].push(expense);
    return acc;
  }, {}) || {};

  // Calculate totals by category
  const categoryTotals = categories?.reduce((acc: Record<string, number>, category) => {
    const categoryExpenses = expensesByCategory[category.name] || [];
    acc[category.name] = categoryExpenses.reduce((total, expense) => total + expense.amount, 0);
    return acc;
  }, {}) || {};

  if (categoriesLoading) {
    return (
      <div className="p-4 lg:p-8">
        <Skeleton className="h-8 w-64 mb-6" />
        <Skeleton className="h-96" />
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Gastos Detallados</h2>
            <p className="text-gray-600 mt-1">Desglose de gastos por categorías</p>
          </div>
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          {categories?.map((category) => (
            <TabsTrigger key={category.id} value={category.id.toString()}>
              {category.name} Categoría
              <Badge variant="secondary" className="ml-2">
                {formatCurrency(categoryTotals[category.name] || 0)}
              </Badge>
            </TabsTrigger>
          ))}
        </TabsList>

        {categories?.map((category) => (
          <TabsContent key={category.id} value={category.id.toString()}>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Gastos de {category.name} Categoría</CardTitle>
                    <p className="text-gray-600 text-sm">
                      Financiado con descuento de {formatCurrency(category.fundingAmount)}
                    </p>
                  </div>
                  <Button 
                    onClick={() => handleAddExpense(category.id)}
                    className="flex items-center"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Agregar Gasto
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {expensesLoading ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <Skeleton key={i} className="h-16" />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {expensesByCategory[category.name]?.length === 0 || !expensesByCategory[category.name] ? (
                      <div className="text-center py-8 text-gray-500">
                        No hay gastos registrados en esta categoría
                      </div>
                    ) : (
                      expensesByCategory[category.name]?.map((expense) => (
                        <div key={expense.id} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                              <Monitor className="w-5 h-5 text-gray-600" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{expense.description}</p>
                              <p className="text-sm text-gray-500">Gasto registrado</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <p className="font-semibold text-gray-900 font-mono">
                                {formatCurrency(expense.amount)}
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteExpense(expense.id)}
                              disabled={deleteExpense.isPending}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}

                {/* Category Summary */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">
                        Total gastado en {category.name} categoría
                      </p>
                      <p className="text-sm text-gray-500">
                        Disponible del descuento: 
                        <span className="font-medium text-green-600 font-mono ml-1">
                          {formatCurrency(category.fundingAmount - (categoryTotals[category.name] || 0))}
                        </span>
                      </p>
                    </div>
                    <p className="text-2xl font-bold text-gray-900 font-mono">
                      {formatCurrency(categoryTotals[category.name] || 0)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Floating Action Button (Mobile) */}
      <Button 
        onClick={() => setExpenseModalOpen(true)}
        className="lg:hidden fixed bottom-20 right-4 w-14 h-14 rounded-full shadow-lg"
        size="icon"
      >
        <Plus className="w-6 h-6" />
      </Button>

      <ExpenseModal 
        open={expenseModalOpen} 
        onOpenChange={setExpenseModalOpen} 
        categories={categories || []}
        selectedCategoryId={selectedCategoryId}
      />
    </div>
  );
}
