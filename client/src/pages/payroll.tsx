import { useState, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { 
  Upload, 
  Download, 
  FileText, 
  FileSpreadsheet, 
  FileCode,
  Calendar,
  User,
  DollarSign,
  Plus,
  Trash2
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/utils";
import type { 
  PayrollReport, 
  PayrollReportWithEntries, 
  Employee 
} from "@shared/schema";

export default function Payroll() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: reports, isLoading: reportsLoading } = useQuery<PayrollReport[]>({
    queryKey: ["/api/payroll-reports"],
  });

  const { data: employees, isLoading: employeesLoading } = useQuery<Employee[]>({
    queryKey: ["/api/employees"],
  });

  const importPayroll = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      
      // Use XMLHttpRequest instead of fetch for better compatibility with file uploads
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        xhr.onload = function() {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              reject(new Error('Error al procesar la respuesta del servidor'));
            }
          } else {
            reject(new Error(`Error del servidor: ${xhr.status} ${xhr.statusText}`));
          }
        };
        
        xhr.onerror = function() {
          reject(new Error('Error de conexión al servidor'));
        };
        
        xhr.ontimeout = function() {
          reject(new Error('Tiempo de espera agotado'));
        };
        
        xhr.timeout = 30000; // 30 seconds timeout
        xhr.open('POST', '/api/payroll/import');
        xhr.send(formData);
      });
    },
    onSuccess: (data: any) => {
      queryClient.invalidateQueries({ queryKey: ["/api/payroll-reports"] });
      queryClient.invalidateQueries({ queryKey: ["/api/employees"] });
      toast({
        title: "Archivo importado exitosamente",
        description: `Se crearon ${data.entriesCreated} entradas de nómina para ${data.employee.name}`,
      });
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    },
    onError: (error) => {
      console.error('Import error:', error);
      toast({
        title: "Error al importar archivo",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    },
  });

  const deleteReport = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/payroll-reports/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/payroll-reports"] });
      toast({
        title: "Informe eliminado",
        description: "El informe de nómina se ha eliminado correctamente.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "No se pudo eliminar el informe. Intenta de nuevo.",
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validExtensions = ['.csv', '.xlsx', '.xls', '.json', '.xml', '.md'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (validExtensions.includes(fileExtension)) {
        setSelectedFile(file);
      } else {
        toast({
          title: "Formato no válido",
          description: "Por favor selecciona un archivo CSV, Excel, JSON, XML o Markdown.",
          variant: "destructive",
        });
      }
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;
    
    setIsImporting(true);
    try {
      await importPayroll.mutateAsync(selectedFile);
    } finally {
      setIsImporting(false);
    }
  };

  const handleExport = async (reportId: number, format: string, year: number, month: number) => {
    try {
      const response = await fetch(`/api/payroll/export/${reportId}/${format}`);
      
      if (!response.ok) {
        throw new Error('Error al exportar archivo');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `nomina-${year}-${month}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Archivo exportado",
        description: `El informe se ha exportado en formato ${format.toUpperCase()}`,
      });
    } catch (error) {
      toast({
        title: "Error al exportar",
        description: "No se pudo exportar el archivo. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteReport = (id: number) => {
    if (confirm("¿Estás seguro de que quieres eliminar este informe?")) {
      deleteReport.mutate(id);
    }
  };

  const getFileIcon = (format: string) => {
    switch (format) {
      case 'csv': return <FileSpreadsheet className="w-4 h-4" />;
      case 'xlsx': return <FileSpreadsheet className="w-4 h-4" />;
      case 'json': return <FileCode className="w-4 h-4" />;
      case 'xml': return <FileCode className="w-4 h-4" />;
      case 'md': return <FileText className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const exportFormats = [
    { name: 'CSV', value: 'csv', description: 'Valores separados por comas' },
    { name: 'Excel', value: 'xlsx', description: 'Hoja de cálculo de Excel' },
    { name: 'JSON', value: 'json', description: 'Notación de objetos JavaScript' },
    { name: 'XML', value: 'xml', description: 'Lenguaje de marcado extensible' },
    { name: 'Markdown', value: 'md', description: 'Formato de texto plano' },
  ];

  if (reportsLoading || employeesLoading) {
    return (
      <div className="p-4 lg:p-8">
        <Skeleton className="h-8 w-64 mb-6" />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Informes de Nómina</h2>
            <p className="text-gray-600 mt-1">Importa y gestiona informes mensuales de nómina</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Import Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="w-5 h-5 mr-2" />
              Importar Archivo de Nómina
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xlsx,.xls,.json,.xml,.md"
                onChange={handleFileSelect}
                className="hidden"
              />
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
                className="w-full"
              >
                <Upload className="w-4 h-4 mr-2" />
                Seleccionar Archivo
              </Button>
            </div>
            
            {selectedFile && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getFileIcon(selectedFile.name.split('.').pop()?.toLowerCase() || '')}
                    <span className="text-sm font-medium">{selectedFile.name}</span>
                  </div>
                  <Badge variant="secondary">
                    {(selectedFile.size / 1024).toFixed(1)} KB
                  </Badge>
                </div>
              </div>
            )}
            
            <div className="text-sm text-gray-500">
              <p className="font-medium mb-2">Formatos compatibles:</p>
              <ul className="space-y-1">
                <li>• CSV (.csv) - Valores separados por comas</li>
                <li>• Excel (.xlsx, .xls) - Hojas de cálculo</li>
                <li>• JSON (.json) - Notación de objetos</li>
                <li>• XML (.xml) - Lenguaje de marcado</li>
                <li>• Markdown (.md) - Formato de texto</li>
              </ul>
            </div>
            
            <Button
              onClick={handleImport}
              disabled={!selectedFile || isImporting}
              className="w-full"
            >
              {isImporting ? "Importando..." : "Importar Archivo"}
            </Button>
          </CardContent>
        </Card>

        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Estadísticas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Calendar className="w-5 h-5 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-blue-600">{reports?.length || 0}</p>
                <p className="text-sm text-gray-600">Informes Totales</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <User className="w-5 h-5 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-green-600">{employees?.length || 0}</p>
                <p className="text-sm text-gray-600">Empleados</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports List */}
      <Card>
        <CardHeader>
          <CardTitle>Informes Registrados</CardTitle>
        </CardHeader>
        <CardContent>
          {reports?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto text-gray-300 mb-4" />
              <p className="text-lg font-medium">No hay informes registrados</p>
              <p className="text-sm">Importa tu primer archivo de nómina para comenzar</p>
            </div>
          ) : (
            <div className="space-y-4">
              {reports?.map((report) => (
                <div key={report.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Calendar className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">
                          Nómina {report.month}/{report.year}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Tasa de cambio: {formatCurrency(report.exchangeRate)} KZ/USD
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        {exportFormats.map((format) => (
                          <Button
                            key={format.value}
                            variant="ghost"
                            size="sm"
                            onClick={() => handleExport(report.id, format.value, report.year, report.month)}
                            title={`Exportar como ${format.description}`}
                          >
                            {getFileIcon(format.value)}
                          </Button>
                        ))}
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteReport(report.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}