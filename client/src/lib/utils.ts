import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

export function formatDate(dateString: string): string {
  const [year, month] = dateString.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1);
  return date.toLocaleDateString('es-ES', { 
    year: 'numeric', 
    month: 'short' 
  });
}

export function formatMonthYear(dateString: string): string {
  const [year, month] = dateString.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1);
  return date.toLocaleDateString('es-ES', { 
    year: 'numeric', 
    month: 'long' 
  });
}

export function getCurrentMonthYear(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
}

export function parseMonthYear(monthYear: string): { year: number; month: number } {
  const [year, month] = monthYear.split('-').map(Number);
  return { year, month };
}

export function getMonthsDifference(from: string, to: string): number {
  const fromDate = parseMonthYear(from);
  const toDate = parseMonthYear(to);
  
  return (toDate.year - fromDate.year) * 12 + (toDate.month - fromDate.month);
}
