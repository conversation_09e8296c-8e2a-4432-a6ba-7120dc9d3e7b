@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(213, 94%, 68%);
  --primary-foreground: hsl(213, 100%, 98%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  --success: hsl(142, 76%, 36%);
  --success-foreground: hsl(138, 76%, 97%);
  --warning: hsl(32, 95%, 44%);
  --warning-foreground: hsl(31, 92%, 95%);
  --chart-1: hsl(213, 94%, 68%);
  --chart-2: hsl(142, 76%, 36%);
  --chart-3: hsl(32, 95%, 44%);
  --chart-4: hsl(350, 89%, 60%);
  --chart-5: hsl(271, 91%, 65%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(213, 94%, 68%);
  --primary-foreground: hsl(213, 100%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  --success: hsl(142, 76%, 36%);
  --success-foreground: hsl(138, 76%, 97%);
  --warning: hsl(32, 95%, 44%);
  --warning-foreground: hsl(31, 92%, 95%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer components {
  .balance-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
  }
  
  .nav-item {
    @apply flex flex-col items-center py-2 px-3 rounded-lg transition-colors;
  }
  
  .nav-item.active {
    @apply text-primary-foreground bg-primary bg-opacity-10 border-t-2 border-primary;
  }
  
  .nav-item:not(.active) {
    @apply text-muted-foreground hover:text-foreground;
  }
  
  .sidebar-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  }
  
  .sidebar-link.active {
    @apply text-primary-foreground bg-primary bg-opacity-10 border-r-2 border-primary;
  }
  
  .sidebar-link:not(.active) {
    @apply text-muted-foreground hover:text-foreground hover:bg-muted;
  }
}
