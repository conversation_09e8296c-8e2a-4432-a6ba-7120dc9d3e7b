import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Dashboard from "@/pages/dashboard";
import Transactions from "@/pages/transactions";
import Expenses from "@/pages/expenses";
import Payroll from "@/pages/payroll";
import NotFound from "@/pages/not-found";
import MobileHeader from "@/components/layout/mobile-header";
import DesktopSidebar from "@/components/layout/desktop-sidebar";
import MobileBottomNav from "@/components/layout/mobile-bottom-nav";

function Router() {
  return (
    <>
      <MobileHeader />
      <div className="lg:flex h-screen lg:h-full">
        <DesktopSidebar />
        <main className="flex-1 lg:ml-64">
          <div className="h-full overflow-y-auto pb-20 lg:pb-0">
            <Switch>
              <Route path="/" component={Dashboard} />
              <Route path="/dashboard" component={Dashboard} />
              <Route path="/transactions" component={Transactions} />
              <Route path="/expenses" component={Expenses} />
              <Route path="/payroll" component={Payroll} />
              <Route component={NotFound} />
            </Switch>
          </div>
        </main>
      </div>
      <MobileBottomNav />
    </>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
