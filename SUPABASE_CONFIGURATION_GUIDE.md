# Self-Hosted Supabase Configuration Guide for WebFlowMaster

## Overview

This guide will help you configure your self-hosted Supabase instance at `/home/<USER>/GithubProjects/supabase/` to work with WebFlowMaster using the "db-finanzas" database.

## Prerequisites

- Self-hosted Supabase instance at `/home/<USER>/GithubProjects/supabase/`
- Docker and Docker Compose installed
- WebFlowMaster project with recent Neon-to-Supabase migration completed

## Step 1: Verify Supabase Instance Location

First, let's check if your Supabase instance exists and is properly configured:

```bash
# Navigate to your Supabase directory
cd /home/<USER>/GithubProjects/supabase/

# Check if Supabase is properly initialized
ls -la

# You should see files like:
# - docker-compose.yml
# - .env (or .env.example)
# - volumes/ directory
# - supabase/ directory (if using Supabase CLI)
```

## Step 2: Configure Supabase Database

### Option A: Using Supabase CLI (Recommended)

```bash
# Navigate to Supabase directory
cd /home/<USER>/GithubProjects/supabase/

# Check Supabase status
supabase status

# If not running, start Supabase
supabase start

# Create the db-finanzas database
supabase db reset --db-url postgresql://postgres:postgres@localhost:54322/postgres
```

### Option B: Using Docker Compose

If you're using Docker Compose directly, update the PostgreSQL configuration:

```bash
# Navigate to Supabase directory
cd /home/<USER>/GithubProjects/supabase/

# Edit the docker-compose.yml file
nano docker-compose.yml
```

Look for the PostgreSQL service and ensure it's configured properly:

```yaml
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres  # We'll create db-finanzas separately
    ports:
      - "54322:5432"
    volumes:
      - db-data:/var/lib/postgresql/data
```

## Step 3: Create the db-finanzas Database

### Method 1: Using psql

```bash
# Connect to PostgreSQL in your Supabase instance
psql -h localhost -p 54322 -U postgres -d postgres

# Create the db-finanzas database
CREATE DATABASE "db-finanzas";

# Grant permissions
GRANT ALL PRIVILEGES ON DATABASE "db-finanzas" TO postgres;

# Exit psql
\q
```

### Method 2: Using Docker exec

```bash
# If Supabase is running in Docker, execute commands directly
docker exec -it supabase_db_1 psql -U postgres -c "CREATE DATABASE \"db-finanzas\";"

# Or connect interactively
docker exec -it supabase_db_1 psql -U postgres
```

## Step 4: Update WebFlowMaster Configuration

The DATABASE_URL has already been updated to:
```
DATABASE_URL=postgresql://postgres:postgresql@localhost:54322/db-finanzas
```

### Verify Supabase Default Ports

Supabase typically uses these ports:
- **PostgreSQL**: 54322
- **API Gateway**: 54321
- **Studio**: 54323
- **Inbucket (Email)**: 54324
- **Edge Functions**: 54325

## Step 5: Get Supabase API Keys

### Method 1: From Supabase Studio

1. Open Supabase Studio: http://localhost:54323
2. Navigate to Settings → API
3. Copy the `anon` and `service_role` keys

### Method 2: From Configuration Files

```bash
# Check your Supabase .env file
cd /home/<USER>/GithubProjects/supabase/
cat .env | grep -E "(ANON_KEY|SERVICE_ROLE_KEY)"
```

### Method 3: Default Development Keys

If using default Supabase setup, these are the standard development keys:

```bash
# Update your WebFlowMaster .env file with actual keys from your instance
SUPABASE_ANON_KEY=your_actual_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here
```

## Step 6: Migrate Database Schema

Run the WebFlowMaster database migration to create tables in the new database:

```bash
# Navigate to WebFlowMaster directory
cd /home/<USER>/GithubProjects/WebFlowMaster/

# Run database migration
npx drizzle-kit push

# Or generate and apply migration
npx drizzle-kit generate
npx drizzle-kit migrate
```

## Step 7: Test Connection

Use the provided connectivity test script:

```bash
# Test database connectivity
node test-connectivity.js

# Test database operations
node test-database-operations.js

# Start the application
npm run dev
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Connection Refused
```bash
# Check if Supabase is running
cd /home/<USER>/GithubProjects/supabase/
supabase status

# Or check Docker containers
docker ps | grep supabase
```

#### 2. Database Does Not Exist
```bash
# Recreate the database
psql -h localhost -p 54322 -U postgres -c "CREATE DATABASE \"db-finanzas\";"
```

#### 3. Permission Denied
```bash
# Check PostgreSQL user permissions
psql -h localhost -p 54322 -U postgres -d db-finanzas -c "\du"
```

#### 4. Port Conflicts
```bash
# Check what's running on Supabase ports
netstat -tulpn | grep -E "(54321|54322|54323)"

# If ports are in use, update your Supabase configuration
```

## Step 8: Verify Configuration

### Check Supabase Services

```bash
# All services should be running
curl http://localhost:54321/health
curl http://localhost:54323  # Should show Supabase Studio
```

### Test Database Connection

```bash
# Test direct PostgreSQL connection
psql -h localhost -p 54322 -U postgres -d db-finanzas -c "SELECT version();"
```

### Verify WebFlowMaster Connection

```bash
# Run the connectivity test
cd /home/<USER>/GithubProjects/WebFlowMaster/
node test-connectivity.js
```

## Next Steps

1. **Start Supabase**: Ensure your Supabase instance is running
2. **Create Database**: Create the "db-finanzas" database
3. **Update Keys**: Get actual API keys from your Supabase instance
4. **Migrate Schema**: Run database migrations
5. **Test Application**: Verify WebFlowMaster works with new configuration

## Configuration Summary

After completing this guide, your configuration should be:

- **Database**: db-finanzas
- **Host**: localhost
- **Port**: 54322 (PostgreSQL)
- **User**: postgres
- **Supabase API**: http://localhost:54321
- **Supabase Studio**: http://localhost:54323

## Support

If you encounter issues:
1. Check Supabase logs: `supabase logs`
2. Verify Docker containers: `docker ps`
3. Test direct database connection
4. Review WebFlowMaster connectivity tests
