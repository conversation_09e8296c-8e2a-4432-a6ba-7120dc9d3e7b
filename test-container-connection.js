#!/usr/bin/env node

/**
 * Test Container Connection
 * Tests connection using container name
 */

import postgres from 'postgres';
import { readFileSync } from 'fs';

// Load environment variables
const envContent = readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});
Object.assign(process.env, envVars);

console.log('🔄 Testing Container Connection');
console.log('=' .repeat(50));

async function testConnection() {
  const DATABASE_URL = process.env.DATABASE_URL;
  console.log(`📍 Database URL: ${DATABASE_URL.replace(/:[^:@]*@/, ':****@')}`);
  
  let sql;
  try {
    sql = postgres(DATABASE_URL, {
      max: 1,
      connect_timeout: 10,
    });
    
    console.log('\n🧪 Testing connection...');
    const result = await sql`SELECT current_database(), current_user, version()`;
    
    console.log('✅ Connection successful!');
    console.log(`📊 Database: ${result[0].current_database}`);
    console.log(`👤 User: ${result[0].current_user}`);
    console.log(`🗄️ PostgreSQL: ${result[0].version.split(' ')[1]}`);
    
    // Test if we can query tables
    console.log('\n🔍 Testing table access...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    console.log(`✅ Found ${tables.length} tables:`);
    tables.forEach(table => console.log(`   📋 ${table.table_name}`));
    
    await sql.end();
    
    console.log('\n🎉 Container connection test successful!');
    return true;
    
  } catch (error) {
    console.error('\n❌ Connection failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    
    if (error.code === 'ENOTFOUND') {
      console.error('\n💡 The container name "supabase-db" cannot be resolved.');
      console.error('   This means WebFlowMaster is not in the same Docker network as Supabase.');
      console.error('   Solutions:');
      console.error('   1. Run WebFlowMaster in Docker and connect to supabase_default network');
      console.error('   2. Expose PostgreSQL port in Supabase configuration');
      console.error('   3. Use a different connection method');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Connection refused - PostgreSQL may not be running or accessible');
    }
    
    if (sql) {
      try {
        await sql.end();
      } catch (e) {
        // Ignore cleanup errors
      }
    }
    
    return false;
  }
}

// Run the test
testConnection().then(success => {
  if (!success) {
    console.log('\n🔧 Alternative solutions:');
    console.log('   1. Expose PostgreSQL port manually:');
    console.log('      docker run -d --name postgres-proxy -p 54322:5432 --network supabase_default alpine/socat tcp-listen:5432,fork tcp-connect:supabase-db:5432');
    console.log('   2. Use Supabase REST API instead of direct PostgreSQL connection');
    console.log('   3. Modify Supabase docker-compose to expose PostgreSQL port');
    process.exit(1);
  }
}).catch(console.error);
