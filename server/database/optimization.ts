import { logger, performanceLogger } from '../utils/logger.js';

/**
 * Database optimization utilities
 * Implements performance monitoring and optimization strategies
 */

/**
 * Query performance monitor
 * Tracks slow queries and database performance
 */
export class QueryPerformanceMonitor {
  private slowQueryThreshold: number;
  private queryStats = new Map<string, {
    count: number;
    totalTime: number;
    averageTime: number;
    slowQueries: number;
  }>();

  constructor(slowQueryThreshold: number = 100) {
    this.slowQueryThreshold = slowQueryThreshold;
  }

  /**
   * Monitor query execution
   */
  async monitorQuery<T>(
    queryName: string,
    query: () => Promise<T>,
    params?: any
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await query();
      const duration = Date.now() - startTime;
      
      this.recordQueryStats(queryName, duration);
      
      if (duration > this.slowQueryThreshold) {
        performanceLogger.slowQuery(queryName, duration, params);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Query execution failed', {
        queryName,
        duration,
        params,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Record query statistics
   */
  private recordQueryStats(queryName: string, duration: number): void {
    const stats = this.queryStats.get(queryName) || {
      count: 0,
      totalTime: 0,
      averageTime: 0,
      slowQueries: 0,
    };

    stats.count++;
    stats.totalTime += duration;
    stats.averageTime = stats.totalTime / stats.count;
    
    if (duration > this.slowQueryThreshold) {
      stats.slowQueries++;
    }

    this.queryStats.set(queryName, stats);
  }

  /**
   * Get query statistics
   */
  getStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [queryName, queryStats] of this.queryStats.entries()) {
      stats[queryName] = {
        ...queryStats,
        slowQueryPercentage: (queryStats.slowQueries / queryStats.count) * 100,
      };
    }
    
    return stats;
  }

  /**
   * Reset statistics
   */
  reset(): void {
    this.queryStats.clear();
  }
}

/**
 * Database connection pool monitor
 */
export class ConnectionPoolMonitor {
  private poolStats = {
    totalConnections: 0,
    activeConnections: 0,
    idleConnections: 0,
    waitingClients: 0,
    totalQueries: 0,
    errors: 0,
  };

  /**
   * Update pool statistics
   */
  updateStats(stats: Partial<typeof this.poolStats>): void {
    Object.assign(this.poolStats, stats);
  }

  /**
   * Get current pool statistics
   */
  getStats(): typeof this.poolStats {
    return { ...this.poolStats };
  }

  /**
   * Check pool health
   */
  checkHealth(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
  } {
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Check connection utilization
    const utilizationRate = this.poolStats.activeConnections / this.poolStats.totalConnections;
    if (utilizationRate > 0.9) {
      issues.push('High connection utilization (>90%)');
      status = 'warning';
    }

    // Check waiting clients
    if (this.poolStats.waitingClients > 0) {
      issues.push(`${this.poolStats.waitingClients} clients waiting for connections`);
      status = 'warning';
    }

    // Check error rate
    const errorRate = this.poolStats.errors / this.poolStats.totalQueries;
    if (errorRate > 0.05) {
      issues.push('High error rate (>5%)');
      status = 'critical';
    }

    return { status, issues };
  }
}

/**
 * Database index recommendations
 */
export class IndexRecommendations {
  private queryPatterns = new Map<string, {
    frequency: number;
    columns: string[];
    table: string;
  }>();

  /**
   * Analyze query for index recommendations
   */
  analyzeQuery(table: string, columns: string[], frequency: number = 1): void {
    const pattern = `${table}:${columns.sort().join(',')}`;
    const existing = this.queryPatterns.get(pattern) || {
      frequency: 0,
      columns,
      table,
    };

    existing.frequency += frequency;
    this.queryPatterns.set(pattern, existing);
  }

  /**
   * Get index recommendations
   */
  getRecommendations(): Array<{
    table: string;
    columns: string[];
    frequency: number;
    priority: 'high' | 'medium' | 'low';
  }> {
    const recommendations = Array.from(this.queryPatterns.values())
      .map(pattern => ({
        table: pattern.table,
        columns: pattern.columns,
        frequency: pattern.frequency,
        priority: this.calculatePriority(pattern.frequency),
      }))
      .sort((a, b) => b.frequency - a.frequency);

    return recommendations;
  }

  private calculatePriority(frequency: number): 'high' | 'medium' | 'low' {
    if (frequency > 100) return 'high';
    if (frequency > 10) return 'medium';
    return 'low';
  }
}

/**
 * Query optimization suggestions
 */
export class QueryOptimizer {
  /**
   * Suggest optimizations for common query patterns
   */
  static getOptimizationSuggestions(): Array<{
    pattern: string;
    suggestion: string;
    impact: 'high' | 'medium' | 'low';
  }> {
    return [
      {
        pattern: 'SELECT * FROM large_table',
        suggestion: 'Select only required columns instead of using SELECT *',
        impact: 'high',
      },
      {
        pattern: 'WHERE column LIKE "%value%"',
        suggestion: 'Use full-text search or avoid leading wildcards',
        impact: 'high',
      },
      {
        pattern: 'ORDER BY without LIMIT',
        suggestion: 'Add LIMIT clause to prevent sorting large result sets',
        impact: 'medium',
      },
      {
        pattern: 'N+1 queries',
        suggestion: 'Use JOIN or batch queries instead of multiple single queries',
        impact: 'high',
      },
      {
        pattern: 'Missing WHERE clause',
        suggestion: 'Add WHERE clause to filter results at database level',
        impact: 'medium',
      },
      {
        pattern: 'Subquery in SELECT',
        suggestion: 'Consider using JOIN instead of correlated subqueries',
        impact: 'medium',
      },
    ];
  }

  /**
   * Analyze query for potential optimizations
   */
  static analyzeQuery(sql: string): string[] {
    const suggestions: string[] = [];
    const upperSql = sql.toUpperCase();

    if (upperSql.includes('SELECT *')) {
      suggestions.push('Consider selecting only required columns');
    }

    if (upperSql.includes('LIKE \'%') && upperSql.includes('%\'')) {
      suggestions.push('Leading wildcards in LIKE queries prevent index usage');
    }

    if (upperSql.includes('ORDER BY') && !upperSql.includes('LIMIT')) {
      suggestions.push('Consider adding LIMIT to ORDER BY queries');
    }

    if (!upperSql.includes('WHERE') && upperSql.includes('SELECT')) {
      suggestions.push('Consider adding WHERE clause to filter results');
    }

    return suggestions;
  }
}

/**
 * Database performance metrics collector
 */
export class DatabaseMetrics {
  private metrics = {
    queries: {
      total: 0,
      successful: 0,
      failed: 0,
      slow: 0,
    },
    connections: {
      created: 0,
      destroyed: 0,
      active: 0,
      idle: 0,
    },
    performance: {
      averageQueryTime: 0,
      totalQueryTime: 0,
      slowestQuery: 0,
      fastestQuery: Infinity,
    },
  };

  /**
   * Record query execution
   */
  recordQuery(duration: number, success: boolean): void {
    this.metrics.queries.total++;
    
    if (success) {
      this.metrics.queries.successful++;
    } else {
      this.metrics.queries.failed++;
    }

    if (duration > 100) { // 100ms threshold
      this.metrics.queries.slow++;
    }

    // Update performance metrics
    this.metrics.performance.totalQueryTime += duration;
    this.metrics.performance.averageQueryTime = 
      this.metrics.performance.totalQueryTime / this.metrics.queries.total;
    
    if (duration > this.metrics.performance.slowestQuery) {
      this.metrics.performance.slowestQuery = duration;
    }
    
    if (duration < this.metrics.performance.fastestQuery) {
      this.metrics.performance.fastestQuery = duration;
    }
  }

  /**
   * Record connection events
   */
  recordConnection(event: 'created' | 'destroyed', activeCount: number, idleCount: number): void {
    this.metrics.connections[event]++;
    this.metrics.connections.active = activeCount;
    this.metrics.connections.idle = idleCount;
  }

  /**
   * Get current metrics
   */
  getMetrics(): typeof this.metrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  reset(): void {
    this.metrics = {
      queries: { total: 0, successful: 0, failed: 0, slow: 0 },
      connections: { created: 0, destroyed: 0, active: 0, idle: 0 },
      performance: { averageQueryTime: 0, totalQueryTime: 0, slowestQuery: 0, fastestQuery: Infinity },
    };
  }
}

/**
 * Global instances
 */
export const queryMonitor = new QueryPerformanceMonitor();
export const connectionMonitor = new ConnectionPoolMonitor();
export const indexRecommendations = new IndexRecommendations();
export const databaseMetrics = new DatabaseMetrics();
