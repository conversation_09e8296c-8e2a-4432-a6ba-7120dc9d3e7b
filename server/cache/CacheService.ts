import { logger } from '../utils/logger.js';
import { config } from '../config/index.js';

/**
 * Cache service interface
 * Provides abstraction for different cache implementations
 */
export interface ICacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  exists(key: string): Promise<boolean>;
  increment(key: string, value?: number): Promise<number>;
  expire(key: string, ttl: number): Promise<void>;
}

/**
 * Memory cache implementation
 * Fallback cache when Redis is not available
 */
export class MemoryCacheService implements ICacheService {
  private cache = new Map<string, { value: any; expires: number }>();
  private defaultTtl: number;

  constructor(defaultTtl: number = 300) {
    this.defaultTtl = defaultTtl;
    
    // Cleanup expired entries every minute
    setInterval(() => this.cleanup(), 60000);
  }

  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.value as T;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const expires = Date.now() + (ttl || this.defaultTtl) * 1000;
    this.cache.set(key, { value, expires });
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
  }

  async exists(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  async increment(key: string, value: number = 1): Promise<number> {
    const current = await this.get<number>(key) || 0;
    const newValue = current + value;
    await this.set(key, newValue);
    return newValue;
  }

  async expire(key: string, ttl: number): Promise<void> {
    const entry = this.cache.get(key);
    if (entry) {
      entry.expires = Date.now() + ttl * 1000;
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.cache.delete(key);
      }
    }
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

/**
 * Redis cache implementation
 * High-performance distributed cache
 */
export class RedisCacheService implements ICacheService {
  private redis: any; // Redis client
  private defaultTtl: number;

  constructor(redisClient: any, defaultTtl: number = 300) {
    this.redis = redisClient;
    this.defaultTtl = defaultTtl;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Redis get error', { key, error });
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      const expiry = ttl || this.defaultTtl;
      await this.redis.setex(key, expiry, serialized);
    } catch (error) {
      logger.error('Redis set error', { key, error });
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      logger.error('Redis delete error', { key, error });
    }
  }

  async clear(): Promise<void> {
    try {
      await this.redis.flushdb();
    } catch (error) {
      logger.error('Redis clear error', { error });
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists error', { key, error });
      return false;
    }
  }

  async increment(key: string, value: number = 1): Promise<number> {
    try {
      return await this.redis.incrby(key, value);
    } catch (error) {
      logger.error('Redis increment error', { key, error });
      return 0;
    }
  }

  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.redis.expire(key, ttl);
    } catch (error) {
      logger.error('Redis expire error', { key, error });
    }
  }
}

/**
 * Cache key generator
 * Provides consistent cache key naming
 */
export class CacheKeyGenerator {
  private prefix: string;

  constructor(prefix: string = 'webflowmaster') {
    this.prefix = prefix;
  }

  user(userId: string): string {
    return `${this.prefix}:user:${userId}`;
  }

  transaction(id: number): string {
    return `${this.prefix}:transaction:${id}`;
  }

  transactions(filters?: any): string {
    const filterStr = filters ? JSON.stringify(filters) : 'all';
    return `${this.prefix}:transactions:${filterStr}`;
  }

  balance(): string {
    return `${this.prefix}:balance:current`;
  }

  balanceHistory(months: number): string {
    return `${this.prefix}:balance:history:${months}`;
  }

  analytics(type: string): string {
    return `${this.prefix}:analytics:${type}`;
  }

  payroll(reportId: number): string {
    return `${this.prefix}:payroll:${reportId}`;
  }

  session(sessionId: string): string {
    return `${this.prefix}:session:${sessionId}`;
  }

  rateLimit(ip: string): string {
    return `${this.prefix}:ratelimit:${ip}`;
  }
}

/**
 * Cache manager with automatic fallback
 * Manages cache operations with error handling
 */
export class CacheManager {
  private cache: ICacheService;
  private keyGenerator: CacheKeyGenerator;

  constructor(cache: ICacheService, keyPrefix?: string) {
    this.cache = cache;
    this.keyGenerator = new CacheKeyGenerator(keyPrefix);
  }

  /**
   * Get or set pattern (cache-aside)
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    try {
      // Try to get from cache first
      const cached = await this.cache.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // Generate value and cache it
      const value = await factory();
      await this.cache.set(key, value, ttl);
      return value;
    } catch (error) {
      logger.error('Cache getOrSet error', { key, error });
      // Fallback to factory function
      return factory();
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  async invalidatePattern(pattern: string): Promise<void> {
    // This would require Redis SCAN command for Redis implementation
    // For memory cache, we can iterate through keys
    logger.info('Cache invalidation requested', { pattern });
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmUp(): Promise<void> {
    logger.info('Starting cache warm-up');
    
    try {
      // Warm up common analytics data
      const balanceKey = this.keyGenerator.balance();
      // This would fetch and cache current balance
      
      logger.info('Cache warm-up completed');
    } catch (error) {
      logger.error('Cache warm-up failed', { error });
    }
  }

  get keys() {
    return this.keyGenerator;
  }

  get service() {
    return this.cache;
  }
}

/**
 * Create cache service based on configuration
 */
export function createCacheService(): ICacheService {
  if (config.cache.redis.enabled && config.cache.redis.url) {
    try {
      // In a real implementation, you would create Redis client here
      // const redis = new Redis(config.cache.redis.url);
      // return new RedisCacheService(redis, config.cache.redis.ttl);
      
      logger.info('Redis cache not implemented, falling back to memory cache');
      return new MemoryCacheService(config.cache.memory.ttl);
    } catch (error) {
      logger.warn('Failed to connect to Redis, using memory cache', { error });
      return new MemoryCacheService(config.cache.memory.ttl);
    }
  }

  return new MemoryCacheService(config.cache.memory.ttl);
}

/**
 * Global cache manager instance
 */
export const cacheManager = new CacheManager(createCacheService());
