import { 
  missions, 
  transactions, 
  expenseCategories, 
  expenses,
  payrollReports,
  employees,
  payrollConcepts,
  payrollEntries,
  type Mission, 
  type InsertMission,
  type Transaction,
  type InsertTransaction,
  type ExpenseCategory,
  type InsertExpenseCategory,
  type Expense,
  type InsertExpense,
  type PayrollReport,
  type InsertPayrollReport,
  type Employee,
  type InsertEmployee,
  type PayrollConcept,
  type InsertPayrollConcept,
  type PayrollEntry,
  type InsertPayrollEntry,
  type MissionWithRelations,
  type TransactionWithMission,
  type ExpenseWithCategory,
  type PayrollReportWithEntries,
  type PayrollEntryWithRelations
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, asc } from "drizzle-orm";

export interface IStorage {
  // Missions
  getMissions(): Promise<Mission[]>;
  getMissionById(id: number): Promise<Mission | undefined>;
  getMissionWithRelations(id: number): Promise<MissionWithRelations | undefined>;
  createMission(mission: InsertMission): Promise<Mission>;
  
  // Transactions
  getTransactions(): Promise<TransactionWithMission[]>;
  getTransactionsByMission(missionId: number): Promise<TransactionWithMission[]>;
  createTransaction(transaction: InsertTransaction): Promise<Transaction>;
  deleteTransaction(id: number): Promise<void>;
  
  // Expense Categories
  getExpenseCategories(): Promise<ExpenseCategory[]>;
  getExpenseCategoriesByMission(missionId: number): Promise<ExpenseCategory[]>;
  createExpenseCategory(category: InsertExpenseCategory): Promise<ExpenseCategory>;
  
  // Expenses
  getExpenses(): Promise<ExpenseWithCategory[]>;
  getExpensesByCategory(categoryId: number): Promise<ExpenseWithCategory[]>;
  createExpense(expense: InsertExpense): Promise<Expense>;
  deleteExpense(id: number): Promise<void>;
  
  // Analytics
  getCurrentBalance(): Promise<number>;
  getTotalSavings(): Promise<number>;
  getBalanceHistory(months: number): Promise<{ date: string; balance: number }[]>;
  
  // Payroll Reports
  getPayrollReports(): Promise<PayrollReport[]>;
  getPayrollReportById(id: number): Promise<PayrollReportWithEntries | undefined>;
  createPayrollReport(report: InsertPayrollReport): Promise<PayrollReport>;
  updatePayrollReport(id: number, report: Partial<InsertPayrollReport>): Promise<PayrollReport>;
  deletePayrollReport(id: number): Promise<void>;
  
  // Employees
  getEmployees(): Promise<Employee[]>;
  getEmployeeById(id: number): Promise<Employee | undefined>;
  createEmployee(employee: InsertEmployee): Promise<Employee>;
  updateEmployee(id: number, employee: Partial<InsertEmployee>): Promise<Employee>;
  deleteEmployee(id: number): Promise<void>;
  
  // Payroll Concepts
  getPayrollConcepts(): Promise<PayrollConcept[]>;
  getPayrollConceptsBySection(section: string): Promise<PayrollConcept[]>;
  createPayrollConcept(concept: InsertPayrollConcept): Promise<PayrollConcept>;
  updatePayrollConcept(id: number, concept: Partial<InsertPayrollConcept>): Promise<PayrollConcept>;
  deletePayrollConcept(id: number): Promise<void>;
  
  // Payroll Entries
  getPayrollEntries(): Promise<PayrollEntryWithRelations[]>;
  getPayrollEntriesByReport(reportId: number): Promise<PayrollEntryWithRelations[]>;
  createPayrollEntry(entry: InsertPayrollEntry): Promise<PayrollEntry>;
  updatePayrollEntry(id: number, entry: Partial<InsertPayrollEntry>): Promise<PayrollEntry>;
  deletePayrollEntry(id: number): Promise<void>;
  bulkCreatePayrollEntries(entries: InsertPayrollEntry[]): Promise<PayrollEntry[]>;
}

export class DatabaseStorage implements IStorage {
  async getMissions(): Promise<Mission[]> {
    return await db.select().from(missions).orderBy(asc(missions.courseNumber));
  }

  async getMissionById(id: number): Promise<Mission | undefined> {
    const [mission] = await db.select().from(missions).where(eq(missions.id, id));
    return mission || undefined;
  }

  async getMissionWithRelations(id: number): Promise<MissionWithRelations | undefined> {
    const mission = await db.query.missions.findFirst({
      where: eq(missions.id, id),
      with: {
        transactions: {
          orderBy: desc(transactions.date),
        },
        expenseCategories: {
          with: {
            expenses: {
              orderBy: desc(expenses.createdAt),
            },
          },
        },
      },
    });
    return mission || undefined;
  }

  async createMission(mission: InsertMission): Promise<Mission> {
    const [newMission] = await db
      .insert(missions)
      .values(mission)
      .returning();
    return newMission;
  }

  async getTransactions(): Promise<TransactionWithMission[]> {
    return await db.query.transactions.findMany({
      with: {
        mission: true,
      },
      orderBy: desc(transactions.date),
    });
  }

  async getTransactionsByMission(missionId: number): Promise<TransactionWithMission[]> {
    return await db.query.transactions.findMany({
      where: eq(transactions.missionId, missionId),
      with: {
        mission: true,
      },
      orderBy: desc(transactions.date),
    });
  }

  async createTransaction(transaction: InsertTransaction): Promise<Transaction> {
    const [newTransaction] = await db
      .insert(transactions)
      .values(transaction)
      .returning();
    return newTransaction;
  }

  async deleteTransaction(id: number): Promise<void> {
    await db.delete(transactions).where(eq(transactions.id, id));
  }

  async getExpenseCategories(): Promise<ExpenseCategory[]> {
    return await db.select().from(expenseCategories);
  }

  async getExpenseCategoriesByMission(missionId: number): Promise<ExpenseCategory[]> {
    return await db.select().from(expenseCategories).where(eq(expenseCategories.missionId, missionId));
  }

  async createExpenseCategory(category: InsertExpenseCategory): Promise<ExpenseCategory> {
    const [newCategory] = await db
      .insert(expenseCategories)
      .values(category)
      .returning();
    return newCategory;
  }

  async getExpenses(): Promise<ExpenseWithCategory[]> {
    return await db.query.expenses.findMany({
      with: {
        category: true,
      },
      orderBy: desc(expenses.createdAt),
    });
  }

  async getExpensesByCategory(categoryId: number): Promise<ExpenseWithCategory[]> {
    return await db.query.expenses.findMany({
      where: eq(expenses.categoryId, categoryId),
      with: {
        category: true,
      },
      orderBy: desc(expenses.createdAt),
    });
  }

  async createExpense(expense: InsertExpense): Promise<Expense> {
    const [newExpense] = await db
      .insert(expenses)
      .values(expense)
      .returning();
    return newExpense;
  }

  async deleteExpense(id: number): Promise<void> {
    await db.delete(expenses).where(eq(expenses.id, id));
  }

  async getCurrentBalance(): Promise<number> {
    const latestTransaction = await db.query.transactions.findFirst({
      orderBy: desc(transactions.date),
    });
    return latestTransaction?.resultingBalance || 0;
  }

  async getTotalSavings(): Promise<number> {
    const savingsTransactions = await db.select().from(transactions).where(eq(transactions.type, 'savings'));
    return savingsTransactions.reduce((total, transaction) => total + transaction.amount, 0);
  }

  async getBalanceHistory(months: number): Promise<{ date: string; balance: number }[]> {
    const allTransactions = await db.select().from(transactions).orderBy(asc(transactions.date));
    
    // Get the last transaction for each month
    const monthlyBalances: { [key: string]: number } = {};
    
    allTransactions.forEach(transaction => {
      monthlyBalances[transaction.date] = transaction.resultingBalance;
    });

    return Object.entries(monthlyBalances)
      .slice(-months)
      .map(([date, balance]) => ({ date, balance }));
  }

  // Payroll Reports Implementation
  async getPayrollReports(): Promise<PayrollReport[]> {
    return await db.select().from(payrollReports).orderBy(desc(payrollReports.year), desc(payrollReports.month));
  }

  async getPayrollReportById(id: number): Promise<PayrollReportWithEntries | undefined> {
    const report = await db.query.payrollReports.findFirst({
      where: eq(payrollReports.id, id),
      with: {
        entries: {
          with: {
            employee: true,
            concept: true,
          },
          orderBy: [asc(payrollEntries.employeeId), asc(payrollEntries.conceptId)],
        },
      },
    });
    return report || undefined;
  }

  async createPayrollReport(report: InsertPayrollReport): Promise<PayrollReport> {
    const [newReport] = await db
      .insert(payrollReports)
      .values(report)
      .returning();
    return newReport;
  }

  async updatePayrollReport(id: number, report: Partial<InsertPayrollReport>): Promise<PayrollReport> {
    const [updatedReport] = await db
      .update(payrollReports)
      .set({ ...report, updatedAt: new Date() })
      .where(eq(payrollReports.id, id))
      .returning();
    return updatedReport;
  }

  async deletePayrollReport(id: number): Promise<void> {
    // Delete related entries first
    await db.delete(payrollEntries).where(eq(payrollEntries.reportId, id));
    await db.delete(payrollReports).where(eq(payrollReports.id, id));
  }

  // Employees Implementation
  async getEmployees(): Promise<Employee[]> {
    return await db.select().from(employees).orderBy(asc(employees.name));
  }

  async getEmployeeById(id: number): Promise<Employee | undefined> {
    const [employee] = await db.select().from(employees).where(eq(employees.id, id));
    return employee || undefined;
  }

  async createEmployee(employee: InsertEmployee): Promise<Employee> {
    const [newEmployee] = await db
      .insert(employees)
      .values(employee)
      .returning();
    return newEmployee;
  }

  async updateEmployee(id: number, employee: Partial<InsertEmployee>): Promise<Employee> {
    const [updatedEmployee] = await db
      .update(employees)
      .set(employee)
      .where(eq(employees.id, id))
      .returning();
    return updatedEmployee;
  }

  async deleteEmployee(id: number): Promise<void> {
    await db.delete(employees).where(eq(employees.id, id));
  }

  // Payroll Concepts Implementation
  async getPayrollConcepts(): Promise<PayrollConcept[]> {
    return await db.select().from(payrollConcepts).orderBy(asc(payrollConcepts.section), asc(payrollConcepts.displayOrder));
  }

  async getPayrollConceptsBySection(section: string): Promise<PayrollConcept[]> {
    return await db.select().from(payrollConcepts)
      .where(eq(payrollConcepts.section, section))
      .orderBy(asc(payrollConcepts.displayOrder));
  }

  async createPayrollConcept(concept: InsertPayrollConcept): Promise<PayrollConcept> {
    const [newConcept] = await db
      .insert(payrollConcepts)
      .values(concept)
      .returning();
    return newConcept;
  }

  async updatePayrollConcept(id: number, concept: Partial<InsertPayrollConcept>): Promise<PayrollConcept> {
    const [updatedConcept] = await db
      .update(payrollConcepts)
      .set(concept)
      .where(eq(payrollConcepts.id, id))
      .returning();
    return updatedConcept;
  }

  async deletePayrollConcept(id: number): Promise<void> {
    await db.delete(payrollConcepts).where(eq(payrollConcepts.id, id));
  }

  // Payroll Entries Implementation
  async getPayrollEntries(): Promise<PayrollEntryWithRelations[]> {
    return await db.query.payrollEntries.findMany({
      with: {
        employee: true,
        concept: true,
        report: true,
      },
      orderBy: [desc(payrollEntries.reportId), asc(payrollEntries.employeeId)],
    });
  }

  async getPayrollEntriesByReport(reportId: number): Promise<PayrollEntryWithRelations[]> {
    return await db.query.payrollEntries.findMany({
      where: eq(payrollEntries.reportId, reportId),
      with: {
        employee: true,
        concept: true,
        report: true,
      },
      orderBy: [asc(payrollEntries.employeeId), asc(payrollEntries.conceptId)],
    });
  }

  async createPayrollEntry(entry: InsertPayrollEntry): Promise<PayrollEntry> {
    const [newEntry] = await db
      .insert(payrollEntries)
      .values(entry)
      .returning();
    return newEntry;
  }

  async updatePayrollEntry(id: number, entry: Partial<InsertPayrollEntry>): Promise<PayrollEntry> {
    const [updatedEntry] = await db
      .update(payrollEntries)
      .set(entry)
      .where(eq(payrollEntries.id, id))
      .returning();
    return updatedEntry;
  }

  async deletePayrollEntry(id: number): Promise<void> {
    await db.delete(payrollEntries).where(eq(payrollEntries.id, id));
  }

  async bulkCreatePayrollEntries(entries: InsertPayrollEntry[]): Promise<PayrollEntry[]> {
    if (entries.length === 0) return [];
    
    const newEntries = await db
      .insert(payrollEntries)
      .values(entries)
      .returning();
    return newEntries;
  }
}

export const storage = new DatabaseStorage();
