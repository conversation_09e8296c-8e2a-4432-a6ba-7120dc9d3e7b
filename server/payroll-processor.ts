import * as XLSX from 'xlsx';
import * as <PERSON> from 'papaparse';
import * as xml2js from 'xml2js';
import type { 
  InsertEmployee, 
  InsertPayrollConcept, 
  InsertPayrollEntry, 
  InsertPayrollReport,
  Employee,
  PayrollConcept
} from '@shared/schema';

export interface PayrollData {
  report: InsertPayrollReport;
  employee: InsertEmployee;
  entries: Array<{
    concept: InsertPayrollConcept;
    entry: Omit<InsertPayrollEntry, 'reportId' | 'employeeId' | 'conceptId'>;
  }>;
}

export class PayrollProcessor {
  
  // Process uploaded file based on extension
  static async processFile(buffer: Buffer, filename: string): Promise<PayrollData> {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'csv':
        return this.processCSV(buffer);
      case 'xlsx':
      case 'xls':
        return this.processExcel(buffer);
      case 'json':
        return this.processJSON(buffer);
      case 'xml':
        return this.processXML(buffer);
      case 'md':
        return this.processMarkdown(buffer);
      default:
        throw new Error(`Formato de archivo no soportado: ${extension}`);
    }
  }

  // Process CSV files
  private static processCSV(buffer: Buffer): Promise<PayrollData> {
    return new Promise((resolve, reject) => {
      const csvText = buffer.toString('utf-8');
      
      Papa.parse(csvText, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            const payrollData = this.parsePayrollData(results.data);
            resolve(payrollData);
          } catch (error) {
            reject(error);
          }
        },
        error: (error) => reject(error)
      });
    });
  }

  // Process Excel files
  private static processExcel(buffer: Buffer): Promise<PayrollData> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      return Promise.resolve(this.parsePayrollData(jsonData));
    } catch (error) {
      return Promise.reject(error);
    }
  }

  // Process JSON files
  private static processJSON(buffer: Buffer): Promise<PayrollData> {
    try {
      const jsonText = buffer.toString('utf-8');
      const jsonData = JSON.parse(jsonText);
      
      return Promise.resolve(this.parsePayrollData(jsonData));
    } catch (error) {
      return Promise.reject(error);
    }
  }

  // Process XML files
  private static async processXML(buffer: Buffer): Promise<PayrollData> {
    try {
      const xmlText = buffer.toString('utf-8');
      const parser = new xml2js.Parser();
      const result = await parser.parseStringPromise(xmlText);
      
      // Extract data from XML structure (adapt based on actual XML format)
      const xmlData = this.extractDataFromXML(result);
      return this.parsePayrollData(xmlData);
    } catch (error) {
      throw error;
    }
  }

  // Process Markdown files
  private static processMarkdown(buffer: Buffer): Promise<PayrollData> {
    try {
      const markdownText = buffer.toString('utf-8');
      const extractedData = this.parseMarkdownTable(markdownText);
      
      return Promise.resolve(this.parsePayrollData(extractedData));
    } catch (error) {
      return Promise.reject(error);
    }
  }

  // Parse markdown table format
  private static parseMarkdownTable(markdown: string): any[] {
    const lines = markdown.split('\n').filter(line => line.trim());
    
    // Extract year and month from header
    const yearMatch = markdown.match(/Año:\s*(\d{4})/);
    const monthMatch = markdown.match(/Mes:\s*(\d{1,2})/);
    
    const year = yearMatch ? parseInt(yearMatch[1]) : new Date().getFullYear();
    const month = monthMatch ? parseInt(monthMatch[1]) : new Date().getMonth() + 1;
    
    // Find table sections
    const employeeTableStart = lines.findIndex(line => line.includes('Brigada') && line.includes('Expediente'));
    const conceptTableStart = lines.findIndex(line => line.includes('Sección') && line.includes('Concepto'));
    
    if (employeeTableStart === -1 || conceptTableStart === -1) {
      throw new Error('No se pudo encontrar las tablas de empleado y conceptos en el markdown');
    }
    
    // Parse employee data
    const employeeDataLine = lines[employeeTableStart + 2]; // Skip header and separator
    const employeeData = employeeDataLine.split('|').map(cell => cell.trim()).filter(cell => cell);
    
    // Parse concept data
    const conceptLines = lines.slice(conceptTableStart + 2).filter(line => 
      line.includes('|') && !line.includes('---')
    );
    
    const data = [];
    
    // Add metadata
    data.push({
      year,
      month,
      exchangeRate: 931.00,
      employee: {
        brigade: employeeData[0] || '',
        fileNumber: employeeData[1] || '',
        name: employeeData[2] || '',
        ci: employeeData[3] || '',
        signature: employeeData[4] || '',
        costCenter: employeeData[5] || '',
        organizationalUnit: employeeData[6] || ''
      }
    });
    
    // Parse concepts
    conceptLines.forEach(line => {
      const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
      if (cells.length >= 5) {
        data.push({
          section: cells[0],
          concept: cells[1],
          amountKz: this.parseAmount(cells[2]),
          amountUsd: this.parseAmount(cells[3]),
          exchangeRate: parseFloat(cells[4]) || 931.00
        });
      }
    });
    
    return data;
  }

  // Extract data from XML structure
  private static extractDataFromXML(xmlResult: any): any[] {
    // This is a generic implementation - adapt based on actual XML structure
    const data = [];
    
    if (xmlResult.payroll) {
      const payroll = xmlResult.payroll;
      
      if (payroll.metadata) {
        data.push({
          year: parseInt(payroll.metadata[0].year[0]) || new Date().getFullYear(),
          month: parseInt(payroll.metadata[0].month[0]) || new Date().getMonth() + 1,
          exchangeRate: parseFloat(payroll.metadata[0].exchangeRate[0]) || 931.00
        });
      }
      
      if (payroll.employee) {
        payroll.employee.forEach((emp: any) => {
          data.push({
            employee: {
              brigade: emp.brigade[0],
              fileNumber: emp.fileNumber[0],
              name: emp.name[0],
              ci: emp.ci[0],
              signature: emp.signature[0],
              costCenter: emp.costCenter[0],
              organizationalUnit: emp.organizationalUnit[0]
            }
          });
        });
      }
      
      if (payroll.entries) {
        payroll.entries.forEach((entry: any) => {
          data.push({
            section: entry.section[0],
            concept: entry.concept[0],
            amountKz: parseFloat(entry.amountKz[0]) || 0,
            amountUsd: parseFloat(entry.amountUsd[0]) || 0,
            exchangeRate: parseFloat(entry.exchangeRate[0]) || 931.00
          });
        });
      }
    }
    
    return data;
  }

  // Parse general payroll data structure
  private static parsePayrollData(data: any[]): PayrollData {
    if (!data || data.length === 0) {
      throw new Error('No se encontraron datos en el archivo');
    }
    
    // Extract metadata
    const metadata = data.find(item => item.year || item.month) || data[0];
    const year = metadata?.year || new Date().getFullYear();
    const month = metadata?.month || new Date().getMonth() + 1;
    const exchangeRate = metadata?.exchangeRate || 931.00;
    
    // Extract employee data
    const employeeData = data.find(item => item.employee) || data.find(item => 
      item.brigade || item.fileNumber || item.name
    );
    
    if (!employeeData) {
      throw new Error('No se encontraron datos del empleado');
    }
    
    const employee: InsertEmployee = {
      brigade: employeeData.employee?.brigade || employeeData.brigade || '',
      fileNumber: employeeData.employee?.fileNumber || employeeData.fileNumber || '',
      name: employeeData.employee?.name || employeeData.name || '',
      ci: employeeData.employee?.ci || employeeData.ci || '',
      signature: employeeData.employee?.signature || employeeData.signature || '',
      costCenter: employeeData.employee?.costCenter || employeeData.costCenter || '',
      organizationalUnit: employeeData.employee?.organizationalUnit || employeeData.organizationalUnit || ''
    };
    
    const report: InsertPayrollReport = {
      year,
      month,
      exchangeRate
    };
    
    // Extract concept entries
    const conceptEntries = data.filter(item => 
      (item.section || item.concept) && (item.amountKz !== undefined || item.amountUsd !== undefined)
    );
    
    const entries = conceptEntries.map(item => ({
      concept: {
        name: item.concept || '',
        section: item.section || 'OTROS',
        isActive: 1,
        displayOrder: 0
      } as InsertPayrollConcept,
      entry: {
        amountKz: item.amountKz || 0,
        amountUsd: item.amountUsd || 0,
        exchangeRate: item.exchangeRate || exchangeRate
      }
    }));
    
    return {
      report,
      employee,
      entries
    };
  }

  // Helper to parse currency amounts
  private static parseAmount(amountStr: string): number {
    if (!amountStr) return 0;
    
    // Remove currency symbols and formatting
    const cleanAmount = amountStr.replace(/[$,\s]/g, '').replace(/[^\d.-]/g, '');
    const amount = parseFloat(cleanAmount);
    
    return isNaN(amount) ? 0 : amount;
  }

  // Generate export data in various formats
  static generateExport(payrollData: PayrollData, format: 'csv' | 'xlsx' | 'json' | 'xml' | 'md'): Buffer {
    switch (format) {
      case 'csv':
        return this.generateCSV(payrollData);
      case 'xlsx':
        return this.generateExcel(payrollData);
      case 'json':
        return this.generateJSON(payrollData);
      case 'xml':
        return this.generateXML(payrollData);
      case 'md':
        return this.generateMarkdown(payrollData);
      default:
        throw new Error(`Formato de exportación no soportado: ${format}`);
    }
  }

  private static generateCSV(data: PayrollData): Buffer {
    const rows = [
      ['Año', 'Mes', 'Brigada', 'Expediente', 'Colaborador', 'CI', 'Firma', 'Centro de Costo', 'Unidad Orgánica'],
      [
        data.report.year,
        data.report.month,
        data.employee.brigade,
        data.employee.fileNumber,
        data.employee.name,
        data.employee.ci,
        data.employee.signature,
        data.employee.costCenter,
        data.employee.organizationalUnit
      ],
      [''],
      ['Sección', 'Concepto', 'Saldo KZ', 'Saldo USD', 'Tasa de Cambio']
    ];
    
    data.entries.forEach(entry => {
      rows.push([
        entry.concept.section,
        entry.concept.name,
        entry.entry.amountKz,
        entry.entry.amountUsd,
        entry.entry.exchangeRate
      ]);
    });
    
    const csv = Papa.unparse(rows);
    return Buffer.from(csv, 'utf-8');
  }

  private static generateExcel(data: PayrollData): Buffer {
    const workbook = XLSX.utils.book_new();
    
    // Employee info sheet
    const employeeData = [
      ['Campo', 'Valor'],
      ['Año', data.report.year],
      ['Mes', data.report.month],
      ['Brigada', data.employee.brigade],
      ['Expediente', data.employee.fileNumber],
      ['Colaborador', data.employee.name],
      ['CI', data.employee.ci],
      ['Firma', data.employee.signature],
      ['Centro de Costo', data.employee.costCenter],
      ['Unidad Orgánica', data.employee.organizationalUnit]
    ];
    
    const employeeSheet = XLSX.utils.aoa_to_sheet(employeeData);
    XLSX.utils.book_append_sheet(workbook, employeeSheet, 'Información');
    
    // Concepts sheet
    const conceptData = [
      ['Sección', 'Concepto', 'Saldo KZ', 'Saldo USD', 'Tasa de Cambio']
    ];
    
    data.entries.forEach(entry => {
      conceptData.push([
        entry.concept.section,
        entry.concept.name,
        entry.entry.amountKz,
        entry.entry.amountUsd,
        entry.entry.exchangeRate
      ]);
    });
    
    const conceptSheet = XLSX.utils.aoa_to_sheet(conceptData);
    XLSX.utils.book_append_sheet(workbook, conceptSheet, 'Conceptos');
    
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  private static generateJSON(data: PayrollData): Buffer {
    const jsonData = {
      metadata: {
        year: data.report.year,
        month: data.report.month,
        exchangeRate: data.report.exchangeRate
      },
      employee: data.employee,
      entries: data.entries.map(entry => ({
        section: entry.concept.section,
        concept: entry.concept.name,
        amountKz: entry.entry.amountKz,
        amountUsd: entry.entry.amountUsd,
        exchangeRate: entry.entry.exchangeRate
      }))
    };
    
    return Buffer.from(JSON.stringify(jsonData, null, 2), 'utf-8');
  }

  private static generateXML(data: PayrollData): Buffer {
    const builder = new xml2js.Builder();
    const xmlData = {
      payroll: {
        metadata: {
          year: data.report.year,
          month: data.report.month,
          exchangeRate: data.report.exchangeRate
        },
        employee: data.employee,
        entries: data.entries.map(entry => ({
          section: entry.concept.section,
          concept: entry.concept.name,
          amountKz: entry.entry.amountKz,
          amountUsd: entry.entry.amountUsd,
          exchangeRate: entry.entry.exchangeRate
        }))
      }
    };
    
    const xml = builder.buildObject(xmlData);
    return Buffer.from(xml, 'utf-8');
  }

  private static generateMarkdown(data: PayrollData): Buffer {
    const md = `## Informe de Resumen Mensual de Nómina

Año: ${data.report.year}  Mes: ${data.report.month}

| Brigada | Expediente | Colaborador | CI | Firma | Centro de Costo | Unidad Orgánica |
| --- | --- | --- | --- | --- | --- | --- |
| ${data.employee.brigade} | ${data.employee.fileNumber} | ${data.employee.name} | ${data.employee.ci} | ${data.employee.signature} | ${data.employee.costCenter} | ${data.employee.organizationalUnit} |

| Sección | Concepto | Saldo KZ | Saldo USD | Tasa de Cambio |
| --- | --- | --- | --- | --- |
${data.entries.map(entry => 
  `| ${entry.concept.section} | ${entry.concept.name} | $ ${entry.entry.amountKz.toFixed(2)} | $ ${entry.entry.amountUsd.toFixed(2)} | ${entry.entry.exchangeRate.toFixed(2)} |`
).join('\n')}
`;
    
    return Buffer.from(md, 'utf-8');
  }
}