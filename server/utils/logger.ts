import winston from 'winston';
import { config } from '../config/index.js';

/**
 * Structured logging system with multiple transports
 * Follows observability best practices
 */

/**
 * Log levels with numeric priorities
 */
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

/**
 * Custom log format for development
 * Human-readable format with colors
 */
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

/**
 * Production log format
 * Structured JSON format for log aggregation
 */
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    return JSON.stringify({
      timestamp,
      level,
      message,
      service: 'webflowmaster',
      environment: config.app.env,
      ...meta,
    });
  })
);

/**
 * Create transports based on environment
 */
const createTransports = (): winston.transport[] => {
  const transports: winston.transport[] = [];

  // Console transport for development
  if (config.logging.enableConsole) {
    transports.push(
      new winston.transports.Console({
        level: config.logging.level,
        format: config.app.isDevelopment ? developmentFormat : productionFormat,
      })
    );
  }

  // File transports for production
  if (config.logging.enableFile) {
    // Error log file
    transports.push(
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: productionFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );

    // Combined log file
    transports.push(
      new winston.transports.File({
        filename: 'logs/combined.log',
        format: productionFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );
  }

  return transports;
};

/**
 * Create Winston logger instance
 */
export const logger = winston.createLogger({
  levels: logLevels,
  level: config.logging.level,
  format: config.app.isProduction ? productionFormat : developmentFormat,
  transports: createTransports(),
  exitOnError: false,
  silent: config.app.isTest,
});

/**
 * Request logging middleware
 * Logs HTTP requests with timing and metadata
 */
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  const { method, url, ip, headers } = req;

  // Log request start
  logger.http('Request started', {
    method,
    url,
    ip,
    userAgent: headers['user-agent'],
    contentLength: headers['content-length'],
    requestId: req.id || generateRequestId(),
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function (chunk: any, encoding: any) {
    const duration = Date.now() - start;
    const { statusCode } = res;

    // Determine log level based on status code
    let level = 'info';
    if (statusCode >= 400 && statusCode < 500) {
      level = 'warn';
    } else if (statusCode >= 500) {
      level = 'error';
    }

    logger.log(level, 'Request completed', {
      method,
      url,
      ip,
      statusCode,
      duration,
      contentLength: res.get('content-length'),
      requestId: req.id || 'unknown',
    });

    originalEnd.call(this, chunk, encoding);
  };

  next();
};

/**
 * Security event logger
 * Logs security-related events for monitoring
 */
export const securityLogger = {
  loginAttempt: (email: string, ip: string, success: boolean, reason?: string) => {
    logger.info('Login attempt', {
      event: 'login_attempt',
      email,
      ip,
      success,
      reason,
      timestamp: new Date().toISOString(),
    });
  },

  loginSuccess: (userId: string, email: string, ip: string) => {
    logger.info('Login successful', {
      event: 'login_success',
      userId,
      email,
      ip,
      timestamp: new Date().toISOString(),
    });
  },

  loginFailure: (email: string, ip: string, reason: string) => {
    logger.warn('Login failed', {
      event: 'login_failure',
      email,
      ip,
      reason,
      timestamp: new Date().toISOString(),
    });
  },

  logout: (userId: string, ip: string) => {
    logger.info('User logout', {
      event: 'logout',
      userId,
      ip,
      timestamp: new Date().toISOString(),
    });
  },

  accessDenied: (userId: string, resource: string, ip: string) => {
    logger.warn('Access denied', {
      event: 'access_denied',
      userId,
      resource,
      ip,
      timestamp: new Date().toISOString(),
    });
  },

  suspiciousActivity: (description: string, ip: string, metadata?: any) => {
    logger.warn('Suspicious activity detected', {
      event: 'suspicious_activity',
      description,
      ip,
      metadata,
      timestamp: new Date().toISOString(),
    });
  },
};

/**
 * Application event logger
 * Logs business logic events
 */
export const appLogger = {
  payrollImport: (userId: string, filename: string, recordCount: number, success: boolean) => {
    logger.info('Payroll import', {
      event: 'payroll_import',
      userId,
      filename,
      recordCount,
      success,
      timestamp: new Date().toISOString(),
    });
  },

  dataExport: (userId: string, type: string, format: string, recordCount: number) => {
    logger.info('Data export', {
      event: 'data_export',
      userId,
      type,
      format,
      recordCount,
      timestamp: new Date().toISOString(),
    });
  },

  databaseOperation: (operation: string, table: string, recordId?: string, success: boolean = true) => {
    logger.debug('Database operation', {
      event: 'database_operation',
      operation,
      table,
      recordId,
      success,
      timestamp: new Date().toISOString(),
    });
  },

  configurationChange: (userId: string, setting: string, oldValue: any, newValue: any) => {
    logger.info('Configuration changed', {
      event: 'configuration_change',
      userId,
      setting,
      oldValue,
      newValue,
      timestamp: new Date().toISOString(),
    });
  },
};

/**
 * Performance logger
 * Logs performance metrics and slow operations
 */
export const performanceLogger = {
  slowQuery: (query: string, duration: number, params?: any) => {
    logger.warn('Slow database query', {
      event: 'slow_query',
      query,
      duration,
      params,
      timestamp: new Date().toISOString(),
    });
  },

  slowRequest: (method: string, url: string, duration: number) => {
    logger.warn('Slow request', {
      event: 'slow_request',
      method,
      url,
      duration,
      timestamp: new Date().toISOString(),
    });
  },

  memoryUsage: () => {
    const usage = process.memoryUsage();
    logger.debug('Memory usage', {
      event: 'memory_usage',
      ...usage,
      timestamp: new Date().toISOString(),
    });
  },
};

/**
 * Error logger with context
 * Logs errors with full context for debugging
 */
export const errorLogger = {
  logError: (error: Error, context?: any) => {
    logger.error('Application error', {
      event: 'application_error',
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
    });
  },

  logValidationError: (errors: any[], context?: any) => {
    logger.warn('Validation error', {
      event: 'validation_error',
      errors,
      context,
      timestamp: new Date().toISOString(),
    });
  },

  logDatabaseError: (error: Error, query?: string, params?: any) => {
    logger.error('Database error', {
      event: 'database_error',
      message: error.message,
      stack: error.stack,
      query,
      params,
      timestamp: new Date().toISOString(),
    });
  },
};

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Graceful shutdown logging
 */
export const gracefulShutdown = () => {
  logger.info('Application shutting down gracefully');
  logger.end();
};
