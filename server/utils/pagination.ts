import { z } from 'zod';

/**
 * Pagination utilities for efficient data handling
 * Implements cursor-based and offset-based pagination
 */

/**
 * Pagination parameters interface
 */
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
  sortBy?: string;
  sortOrder: 'asc' | 'desc';
}

/**
 * Pagination result interface
 */
export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextPage?: number;
    prevPage?: number;
  };
}

/**
 * Cursor pagination interface
 */
export interface CursorPaginationParams {
  limit: number;
  cursor?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

/**
 * Cursor pagination result interface
 */
export interface CursorPaginationResult<T> {
  data: T[];
  pagination: {
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextCursor?: string;
    prevCursor?: string;
  };
}

/**
 * Pagination validation schema
 */
export const paginationSchema = z.object({
  page: z.string().optional().default('1').transform(Number),
  limit: z.string().optional().default('10').transform(Number),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
}).refine(data => data.page > 0, {
  message: 'Page must be greater than 0',
  path: ['page'],
}).refine(data => data.limit > 0 && data.limit <= 100, {
  message: 'Limit must be between 1 and 100',
  path: ['limit'],
});

/**
 * Cursor pagination validation schema
 */
export const cursorPaginationSchema = z.object({
  limit: z.string().optional().default('10').transform(Number),
  cursor: z.string().optional(),
  sortBy: z.string().default('id'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
}).refine(data => data.limit > 0 && data.limit <= 100, {
  message: 'Limit must be between 1 and 100',
  path: ['limit'],
});

/**
 * Pagination utility class
 */
export class PaginationHelper {
  /**
   * Parse pagination parameters from query
   */
  static parsePaginationParams(query: any): PaginationParams {
    const validated = paginationSchema.parse(query);
    const offset = (validated.page - 1) * validated.limit;
    
    return {
      page: validated.page,
      limit: validated.limit,
      offset,
      sortBy: validated.sortBy,
      sortOrder: validated.sortOrder,
    };
  }

  /**
   * Parse cursor pagination parameters from query
   */
  static parseCursorPaginationParams(query: any): CursorPaginationParams {
    return cursorPaginationSchema.parse(query);
  }

  /**
   * Create pagination result
   */
  static createPaginationResult<T>(
    data: T[],
    total: number,
    params: PaginationParams
  ): PaginationResult<T> {
    const totalPages = Math.ceil(total / params.limit);
    const hasNext = params.page < totalPages;
    const hasPrev = params.page > 1;

    return {
      data,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
        nextPage: hasNext ? params.page + 1 : undefined,
        prevPage: hasPrev ? params.page - 1 : undefined,
      },
    };
  }

  /**
   * Create cursor pagination result
   */
  static createCursorPaginationResult<T>(
    data: T[],
    params: CursorPaginationParams,
    getCursor: (item: T) => string
  ): CursorPaginationResult<T> {
    const hasNext = data.length === params.limit + 1;
    const hasPrev = !!params.cursor;

    // Remove extra item used for hasNext detection
    if (hasNext) {
      data.pop();
    }

    const nextCursor = hasNext && data.length > 0 
      ? getCursor(data[data.length - 1]) 
      : undefined;
    
    const prevCursor = hasPrev && data.length > 0 
      ? getCursor(data[0]) 
      : undefined;

    return {
      data,
      pagination: {
        limit: params.limit,
        hasNext,
        hasPrev,
        nextCursor,
        prevCursor,
      },
    };
  }

  /**
   * Generate SQL LIMIT and OFFSET clause
   */
  static generateLimitOffset(params: PaginationParams): string {
    return `LIMIT ${params.limit} OFFSET ${params.offset}`;
  }

  /**
   * Generate SQL ORDER BY clause
   */
  static generateOrderBy(params: PaginationParams, allowedColumns: string[]): string {
    if (!params.sortBy || !allowedColumns.includes(params.sortBy)) {
      return '';
    }

    return `ORDER BY ${params.sortBy} ${params.sortOrder.toUpperCase()}`;
  }

  /**
   * Generate cursor-based WHERE clause
   */
  static generateCursorWhere(
    params: CursorPaginationParams,
    cursorColumn: string
  ): { where: string; values: any[] } {
    if (!params.cursor) {
      return { where: '', values: [] };
    }

    const operator = params.sortOrder === 'asc' ? '>' : '<';
    return {
      where: `WHERE ${cursorColumn} ${operator} ?`,
      values: [params.cursor],
    };
  }

  /**
   * Calculate pagination metadata
   */
  static calculateMetadata(page: number, limit: number, total: number) {
    const totalPages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    
    return {
      page,
      limit,
      offset,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
      startIndex: offset + 1,
      endIndex: Math.min(offset + limit, total),
    };
  }
}

/**
 * Advanced pagination with search and filters
 */
export class AdvancedPagination {
  /**
   * Build complex query with pagination, search, and filters
   */
  static buildQuery(
    baseQuery: string,
    params: PaginationParams,
    searchParams?: {
      searchTerm?: string;
      searchColumns: string[];
    },
    filters?: Record<string, any>,
    allowedSortColumns: string[] = []
  ): { query: string; countQuery: string; values: any[] } {
    let query = baseQuery;
    let whereConditions: string[] = [];
    let values: any[] = [];

    // Add search conditions
    if (searchParams?.searchTerm && searchParams.searchColumns.length > 0) {
      const searchConditions = searchParams.searchColumns
        .map(column => `${column} ILIKE ?`)
        .join(' OR ');
      whereConditions.push(`(${searchConditions})`);
      
      // Add search term for each column
      searchParams.searchColumns.forEach(() => {
        values.push(`%${searchParams.searchTerm}%`);
      });
    }

    // Add filter conditions
    if (filters) {
      Object.entries(filters).forEach(([column, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            whereConditions.push(`${column} IN (${value.map(() => '?').join(', ')})`);
            values.push(...value);
          } else {
            whereConditions.push(`${column} = ?`);
            values.push(value);
          }
        }
      });
    }

    // Add WHERE clause if conditions exist
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // Add ORDER BY clause
    if (params.sortBy && allowedSortColumns.includes(params.sortBy)) {
      query += ` ORDER BY ${params.sortBy} ${params.sortOrder.toUpperCase()}`;
    }

    // Create count query for total records
    const countQuery = query.replace(/SELECT .+ FROM/, 'SELECT COUNT(*) as total FROM')
                           .replace(/ORDER BY .+$/, '');

    // Add pagination
    query += ` LIMIT ${params.limit} OFFSET ${params.offset}`;

    return { query, countQuery, values };
  }
}

/**
 * Pagination middleware for Express
 */
export function paginationMiddleware(req: any, res: any, next: any) {
  try {
    req.pagination = PaginationHelper.parsePaginationParams(req.query);
    next();
  } catch (error) {
    res.status(400).json({
      error: 'Invalid pagination parameters',
      details: error instanceof z.ZodError ? error.errors : [],
    });
  }
}

/**
 * Cursor pagination middleware for Express
 */
export function cursorPaginationMiddleware(req: any, res: any, next: any) {
  try {
    req.cursorPagination = PaginationHelper.parseCursorPaginationParams(req.query);
    next();
  } catch (error) {
    res.status(400).json({
      error: 'Invalid cursor pagination parameters',
      details: error instanceof z.ZodError ? error.errors : [],
    });
  }
}
