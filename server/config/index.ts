import { z } from 'zod';
import { config as dotenvConfig } from 'dotenv';

// Load environment variables from .env file
dotenvConfig();

/**
 * Environment configuration schema with validation
 * Follows security-first design with required environment variables
 */
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('5000'),
  
  // Database configuration
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  
  // Security configuration
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  SESSION_SECRET: z.string().min(32, 'SESSION_SECRET must be at least 32 characters'),
  
  // CORS configuration
  ALLOWED_ORIGINS: z.string().optional().default('http://localhost:3000,http://localhost:5000'),
  
  // Rate limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // File upload limits
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  ALLOWED_FILE_TYPES: z.string().default('csv,xlsx,xls,json,xml,md'),
  
  // Redis configuration (optional for caching)
  REDIS_URL: z.string().optional(),
  
  // Logging configuration
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Monitoring
  ENABLE_METRICS: z.string().transform(Boolean).default('true'),
});

/**
 * Validates and parses environment variables
 * Throws error if required variables are missing or invalid
 */
function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
}

export const env = validateEnv();

/**
 * Application configuration object
 * Centralizes all configuration with type safety
 */
export const config = {
  app: {
    env: env.NODE_ENV,
    port: env.PORT,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isTest: env.NODE_ENV === 'test',
  },
  
  database: {
    url: env.DATABASE_URL,
    ssl: env.NODE_ENV === 'production',
    pool: {
      min: 2,
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    },
  },
  
  security: {
    jwt: {
      secret: env.JWT_SECRET,
      expiresIn: '24h',
      algorithm: 'HS256' as const,
    },
    session: {
      secret: env.SESSION_SECRET,
      name: 'webflowmaster.sid',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      secure: env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'strict' as const,
    },
    cors: {
      origin: env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      maxAge: 86400, // 24 hours
    },
    rateLimit: {
      windowMs: env.RATE_LIMIT_WINDOW_MS,
      max: env.RATE_LIMIT_MAX_REQUESTS,
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    },
  },
  
  upload: {
    maxFileSize: env.MAX_FILE_SIZE,
    allowedTypes: env.ALLOWED_FILE_TYPES.split(',').map(type => type.trim()),
    destination: './uploads',
    limits: {
      fileSize: env.MAX_FILE_SIZE,
      files: 1,
    },
  },
  
  cache: {
    redis: {
      url: env.REDIS_URL,
      enabled: !!env.REDIS_URL,
      ttl: 3600, // 1 hour default TTL
    },
    memory: {
      enabled: !env.REDIS_URL, // Fallback to memory cache if Redis not available
      maxSize: 100, // Maximum number of items in memory cache
      ttl: 300, // 5 minutes for memory cache
    },
  },
  
  logging: {
    level: env.LOG_LEVEL,
    format: env.NODE_ENV === 'production' ? 'json' : 'combined',
    enableConsole: env.NODE_ENV !== 'production',
    enableFile: env.NODE_ENV === 'production',
  },
  
  monitoring: {
    enabled: env.ENABLE_METRICS,
    healthCheck: {
      path: '/health',
      interval: 30000, // 30 seconds
    },
    metrics: {
      path: '/metrics',
      enabled: env.ENABLE_METRICS,
    },
  },
} as const;

/**
 * Type-safe configuration access
 */
export type Config = typeof config;

/**
 * Validates that all required configuration is present
 * Should be called during application startup
 */
export function validateConfig(): void {
  // Validate JWT secret strength
  if (config.security.jwt.secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }
  
  // Validate session secret strength
  if (config.security.session.secret.length < 32) {
    throw new Error('SESSION_SECRET must be at least 32 characters long');
  }
  
  // Validate database URL format
  if (!config.database.url.startsWith('postgresql://') && !config.database.url.startsWith('postgres://')) {
    throw new Error('DATABASE_URL must be a valid PostgreSQL connection string');
  }
  
  console.log('✅ Configuration validation passed');
}
