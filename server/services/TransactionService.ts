import { CrudService, IRepository } from './BaseService.js';
import { 
  Transaction, 
  InsertTransaction, 
  TransactionWithMission,
  insertTransactionSchema 
} from '@shared/schema.js';
import { businessSchemas } from '../middleware/validation.js';
import { DatabaseStorage } from '../storage.js';

/**
 * Transaction repository interface
 * Defines data access contract for transactions
 */
export interface ITransactionRepository extends IRepository<Transaction, InsertTransaction> {
  findByMission(missionId: number): Promise<TransactionWithMission[]>;
  getCurrentBalance(): Promise<number>;
  getTotalSavings(): Promise<number>;
  getBalanceHistory(months: number): Promise<{ date: string; balance: number }[]>;
}

/**
 * Transaction repository implementation
 * Implements data access layer for transactions
 */
export class TransactionRepository implements ITransactionRepository {
  constructor(private storage: DatabaseStorage) {}

  async findAll(): Promise<Transaction[]> {
    return this.storage.getTransactions();
  }

  async findById(id: number): Promise<Transaction | null> {
    const transactions = await this.storage.getTransactions();
    return transactions.find(t => t.id === id) || null;
  }

  async create(data: InsertTransaction): Promise<Transaction> {
    return this.storage.createTransaction(data);
  }

  async update(id: number, data: Partial<InsertTransaction>): Promise<Transaction> {
    // Note: Update functionality would need to be implemented in storage
    throw new Error('Transaction updates not implemented');
  }

  async delete(id: number): Promise<void> {
    return this.storage.deleteTransaction(id);
  }

  async findByMission(missionId: number): Promise<TransactionWithMission[]> {
    return this.storage.getTransactionsByMission(missionId);
  }

  async getCurrentBalance(): Promise<number> {
    return this.storage.getCurrentBalance();
  }

  async getTotalSavings(): Promise<number> {
    return this.storage.getTotalSavings();
  }

  async getBalanceHistory(months: number): Promise<{ date: string; balance: number }[]> {
    return this.storage.getBalanceHistory(months);
  }
}

/**
 * Transaction service implementing business logic
 * Follows Single Responsibility Principle
 */
export class TransactionService extends CrudService<Transaction, InsertTransaction> {
  protected repository: ITransactionRepository;

  constructor(storage: DatabaseStorage) {
    super('TransactionService');
    this.repository = new TransactionRepository(storage);
  }

  /**
   * Create transaction with balance calculation
   * Implements business rule for balance calculation
   */
  async create(data: InsertTransaction): Promise<Transaction> {
    return this.executeOperation(
      async () => {
        // Validate transaction data
        this.validateInput(data, this.getCreateSchema());

        // Calculate resulting balance
        const currentBalance = await this.repository.getCurrentBalance();
        const newBalance = data.type === 'savings' 
          ? currentBalance + data.amount
          : currentBalance - data.amount;

        // Create transaction with calculated balance
        const transactionData = {
          ...data,
          resultingBalance: newBalance,
        };

        const result = await this.repository.create(transactionData);
        
        this.logBusinessEvent('transaction_created', {
          id: result.id,
          type: data.type,
          amount: data.amount,
          newBalance,
        });

        return result;
      },
      'createWithBalance',
      { data }
    );
  }

  /**
   * Get transactions by mission
   */
  async findByMission(missionId: number): Promise<TransactionWithMission[]> {
    return this.executeOperation(
      () => this.repository.findByMission(missionId),
      'findByMission',
      { missionId }
    );
  }

  /**
   * Get current balance
   */
  async getCurrentBalance(): Promise<number> {
    return this.executeOperation(
      () => this.repository.getCurrentBalance(),
      'getCurrentBalance'
    );
  }

  /**
   * Get total savings
   */
  async getTotalSavings(): Promise<number> {
    return this.executeOperation(
      () => this.repository.getTotalSavings(),
      'getTotalSavings'
    );
  }

  /**
   * Get balance history for analytics
   */
  async getBalanceHistory(months: number = 12): Promise<{ date: string; balance: number }[]> {
    return this.executeOperation(
      () => this.repository.getBalanceHistory(months),
      'getBalanceHistory',
      { months }
    );
  }

  /**
   * Get balance analytics
   */
  async getBalanceAnalytics(): Promise<{
    currentBalance: number;
    totalSavings: number;
    halfBalance: number;
  }> {
    return this.executeOperation(
      async () => {
        const currentBalance = await this.repository.getCurrentBalance();
        const totalSavings = await this.repository.getTotalSavings();
        const halfBalance = currentBalance / 2;

        return {
          currentBalance,
          totalSavings,
          halfBalance,
        };
      },
      'getBalanceAnalytics'
    );
  }

  /**
   * Validate transaction business rules
   */
  private validateTransactionRules(data: InsertTransaction): void {
    // Business rule: Amount must be positive
    if (data.amount <= 0) {
      throw new Error('Transaction amount must be positive');
    }

    // Business rule: Date format validation
    const dateRegex = /^\d{4}-\d{2}$/;
    if (!dateRegex.test(data.date)) {
      throw new Error('Date must be in YYYY-MM format');
    }

    // Business rule: Type validation
    if (!['savings', 'discount'].includes(data.type)) {
      throw new Error('Transaction type must be either "savings" or "discount"');
    }
  }

  /**
   * Get validation schema for create operations
   */
  protected getCreateSchema(): any {
    return businessSchemas.transaction;
  }

  /**
   * Override create to include business rule validation
   */
  async createTransaction(data: InsertTransaction): Promise<Transaction> {
    // Apply business rules validation
    this.validateTransactionRules(data);
    
    // Use parent create method with balance calculation
    return this.create(data);
  }

  /**
   * Check if transaction can be deleted
   */
  async canDelete(id: number): Promise<boolean> {
    const transaction = await this.findById(id);
    if (!transaction) {
      return false;
    }

    // Business rule: Can only delete recent transactions
    const transactionDate = new Date(transaction.date + '-01');
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    return transactionDate >= oneMonthAgo;
  }

  /**
   * Safe delete with business rules
   */
  async safeDelete(id: number): Promise<void> {
    const canDelete = await this.canDelete(id);
    if (!canDelete) {
      throw new Error('Cannot delete transaction older than one month');
    }

    await this.delete(id);
  }
}
