import { Request, Response, NextFunction } from 'express';
import { z, ZodError } from 'zod';
import { logger, errorLogger } from '../utils/logger.js';
import { AuthenticatedRequest } from '../middleware/auth.js';

/**
 * Base controller class implementing common patterns
 * Follows Single Responsibility Principle and DRY
 */
export abstract class BaseController {
  protected readonly controllerName: string;

  constructor(controllerName: string) {
    this.controllerName = controllerName;
  }

  /**
   * Execute controller action with error handling
   * Implements common cross-cutting concerns
   */
  protected async executeAction(
    req: Request | AuthenticatedRequest,
    res: Response,
    action: () => Promise<any>,
    actionName: string
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.debug(`${this.controllerName}: Starting ${actionName}`, {
        method: req.method,
        url: req.url,
        ip: req.ip,
      });

      const result = await action();
      
      const duration = Date.now() - startTime;
      logger.debug(`${this.controllerName}: Completed ${actionName}`, {
        duration,
        statusCode: res.statusCode,
      });

      // If result is not already sent, send it
      if (!res.headersSent) {
        res.json(result);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      
      errorLogger.logError(error as Error, {
        controller: this.controllerName,
        action: actionName,
        method: req.method,
        url: req.url,
        ip: req.ip,
        duration,
      });

      this.handleError(error, res);
    }
  }

  /**
   * Handle different types of errors
   */
  protected handleError(error: any, res: Response): void {
    if (res.headersSent) {
      return;
    }

    // Validation errors
    if (error instanceof ZodError) {
      res.status(400).json({
        error: 'Validation failed',
        message: 'Request data is invalid',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        })),
      });
      return;
    }

    // Business logic errors
    if (error.name === 'BusinessError' || error.message.includes('not found')) {
      res.status(404).json({
        error: 'Resource not found',
        message: error.message,
      });
      return;
    }

    // Authorization errors
    if (error.message.includes('permission') || error.message.includes('unauthorized')) {
      res.status(403).json({
        error: 'Access denied',
        message: error.message,
      });
      return;
    }

    // Default server error
    res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
    });
  }

  /**
   * Extract pagination parameters from query
   */
  protected getPaginationParams(req: Request): {
    page: number;
    limit: number;
    offset: number;
    sortBy?: string;
    sortOrder: 'asc' | 'desc';
  } {
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 10));
    const offset = (page - 1) * limit;
    const sortBy = req.query.sortBy as string;
    const sortOrder = (req.query.sortOrder as string) === 'desc' ? 'desc' : 'asc';

    return { page, limit, offset, sortBy, sortOrder };
  }

  /**
   * Extract filters from query parameters
   */
  protected getFilters(req: Request, allowedFilters: string[]): Record<string, any> {
    const filters: Record<string, any> = {};
    
    for (const filter of allowedFilters) {
      if (req.query[filter] !== undefined) {
        filters[filter] = req.query[filter];
      }
    }

    return filters;
  }

  /**
   * Send paginated response
   */
  protected sendPaginatedResponse<T>(
    res: Response,
    data: T[],
    total: number,
    page: number,
    limit: number
  ): void {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    res.json({
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  }

  /**
   * Send success response with message
   */
  protected sendSuccess(res: Response, data?: any, message?: string): void {
    res.json({
      success: true,
      message: message || 'Operation completed successfully',
      ...(data && { data }),
    });
  }

  /**
   * Send created response
   */
  protected sendCreated(res: Response, data: any, message?: string): void {
    res.status(201).json({
      success: true,
      message: message || 'Resource created successfully',
      data,
    });
  }

  /**
   * Send no content response
   */
  protected sendNoContent(res: Response): void {
    res.status(204).send();
  }

  /**
   * Validate request parameters
   */
  protected validateParams<T>(req: Request, schema: z.ZodSchema<T>): T {
    return schema.parse(req.params);
  }

  /**
   * Validate request body
   */
  protected validateBody<T>(req: Request, schema: z.ZodSchema<T>): T {
    return schema.parse(req.body);
  }

  /**
   * Validate query parameters
   */
  protected validateQuery<T>(req: Request, schema: z.ZodSchema<T>): T {
    return schema.parse(req.query);
  }

  /**
   * Get user from authenticated request
   */
  protected getUser(req: AuthenticatedRequest): any {
    if (!req.user) {
      throw new Error('User not authenticated');
    }
    return req.user;
  }

  /**
   * Check if user has permission
   */
  protected requirePermission(req: AuthenticatedRequest, permission: string): void {
    const user = this.getUser(req);
    // Permission check logic would go here
    // For now, just check if user exists
    if (!user) {
      throw new Error('Insufficient permissions');
    }
  }
}

/**
 * CRUD controller implementing common CRUD operations
 * Follows Open/Closed Principle
 */
export abstract class CrudController<T, CreateT, UpdateT> extends BaseController {
  protected abstract service: any; // Service interface

  /**
   * Get all entities
   */
  async index(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const pagination = this.getPaginationParams(req);
      const filters = this.getFilters(req, this.getAllowedFilters());
      
      const entities = await this.service.findAll(filters);
      
      // For simplicity, return all entities without pagination
      // In a real implementation, you'd implement proper pagination
      return {
        data: entities,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: entities.length,
          totalPages: Math.ceil(entities.length / pagination.limit),
        },
      };
    }, 'index');
  }

  /**
   * Get entity by ID
   */
  async show(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { id } = this.validateParams(req, z.object({ id: z.string().transform(Number) }));
      const entity = await this.service.findByIdOrThrow(id);
      return { data: entity };
    }, 'show');
  }

  /**
   * Create new entity
   */
  async create(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const data = this.validateBody(req, this.getCreateSchema());
      const entity = await this.service.create(data);
      res.status(201);
      return { data: entity };
    }, 'create');
  }

  /**
   * Update entity
   */
  async update(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { id } = this.validateParams(req, z.object({ id: z.string().transform(Number) }));
      const data = this.validateBody(req, this.getUpdateSchema());
      const entity = await this.service.update(id, data);
      return { data: entity };
    }, 'update');
  }

  /**
   * Delete entity
   */
  async destroy(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { id } = this.validateParams(req, z.object({ id: z.string().transform(Number) }));
      await this.service.delete(id);
      res.status(204);
      return null;
    }, 'destroy');
  }

  /**
   * Get allowed filters for the entity
   * Should be implemented by concrete controllers
   */
  protected abstract getAllowedFilters(): string[];

  /**
   * Get validation schema for create operations
   * Should be implemented by concrete controllers
   */
  protected abstract getCreateSchema(): z.ZodSchema<CreateT>;

  /**
   * Get validation schema for update operations
   * Should be implemented by concrete controllers
   */
  protected abstract getUpdateSchema(): z.ZodSchema<UpdateT>;
}
