import { Request, Response } from 'express';
import { z } from 'zod';
import { <PERSON>rud<PERSON>ontroller } from './BaseController.js';
import { TransactionService } from '../services/TransactionService.js';
import { Transaction, InsertTransaction } from '@shared/schema.js';
import { businessSchemas } from '../middleware/validation.js';
import { AuthenticatedRequest, requireWrite, requireRead } from '../middleware/auth.js';

/**
 * Transaction controller implementing REST API endpoints
 * Follows RESTful conventions and separation of concerns
 */
export class TransactionController extends CrudController<Transaction, InsertTransaction, Partial<InsertTransaction>> {
  protected service: TransactionService;

  constructor(transactionService: TransactionService) {
    super('TransactionController');
    this.service = transactionService;
  }

  /**
   * Get all transactions with optional mission filter
   * GET /api/transactions?missionId=1
   */
  async index(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { missionId } = this.validateQuery(req, z.object({
        missionId: z.string().optional().transform(val => val ? Number(val) : undefined),
      }));

      let transactions;
      if (missionId) {
        transactions = await this.service.findByMission(missionId);
      } else {
        transactions = await this.service.findAll();
      }

      return { data: transactions };
    }, 'index');
  }

  /**
   * Create new transaction with balance calculation
   * POST /api/transactions
   */
  async create(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const data = this.validateBody(req, this.getCreateSchema());
      const transaction = await this.service.createTransaction(data);
      res.status(201);
      return { 
        data: transaction,
        message: 'Transaction created successfully'
      };
    }, 'create');
  }

  /**
   * Get balance analytics
   * GET /api/transactions/analytics/balance
   */
  async getBalanceAnalytics(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const analytics = await this.service.getBalanceAnalytics();
      return { data: analytics };
    }, 'getBalanceAnalytics');
  }

  /**
   * Get balance history for charts
   * GET /api/transactions/analytics/balance-history?months=12
   */
  async getBalanceHistory(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { months } = this.validateQuery(req, z.object({
        months: z.string().optional().default('12').transform(Number),
      }));

      const history = await this.service.getBalanceHistory(months);
      return { data: history };
    }, 'getBalanceHistory');
  }

  /**
   * Safe delete transaction with business rules
   * DELETE /api/transactions/:id
   */
  async destroy(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { id } = this.validateParams(req, z.object({ 
        id: z.string().transform(Number) 
      }));

      await this.service.safeDelete(id);
      res.status(204);
      return null;
    }, 'destroy');
  }

  /**
   * Check if transaction can be deleted
   * GET /api/transactions/:id/can-delete
   */
  async canDelete(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const { id } = this.validateParams(req, z.object({ 
        id: z.string().transform(Number) 
      }));

      const canDelete = await this.service.canDelete(id);
      return { 
        data: { canDelete },
        message: canDelete ? 'Transaction can be deleted' : 'Transaction cannot be deleted'
      };
    }, 'canDelete');
  }

  /**
   * Get current balance
   * GET /api/transactions/balance
   */
  async getCurrentBalance(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const balance = await this.service.getCurrentBalance();
      return { data: { balance } };
    }, 'getCurrentBalance');
  }

  /**
   * Get total savings
   * GET /api/transactions/savings
   */
  async getTotalSavings(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const savings = await this.service.getTotalSavings();
      return { data: { savings } };
    }, 'getTotalSavings');
  }

  /**
   * Bulk create transactions
   * POST /api/transactions/bulk
   */
  async bulkCreate(req: AuthenticatedRequest, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      this.requirePermission(req, 'write');
      
      const { transactions } = this.validateBody(req, z.object({
        transactions: z.array(this.getCreateSchema()),
      }));

      const results = [];
      for (const transactionData of transactions) {
        const transaction = await this.service.createTransaction(transactionData);
        results.push(transaction);
      }

      res.status(201);
      return { 
        data: results,
        message: `${results.length} transactions created successfully`
      };
    }, 'bulkCreate');
  }

  /**
   * Get transactions summary by type
   * GET /api/transactions/summary
   */
  async getSummary(req: Request, res: Response): Promise<void> {
    await this.executeAction(req, res, async () => {
      const transactions = await this.service.findAll();
      
      const summary = transactions.reduce((acc, transaction) => {
        if (transaction.type === 'savings') {
          acc.totalSavings += transaction.amount;
          acc.savingsCount++;
        } else {
          acc.totalDiscounts += transaction.amount;
          acc.discountsCount++;
        }
        return acc;
      }, {
        totalSavings: 0,
        totalDiscounts: 0,
        savingsCount: 0,
        discountsCount: 0,
      });

      return { data: summary };
    }, 'getSummary');
  }

  /**
   * Get allowed filters for transactions
   */
  protected getAllowedFilters(): string[] {
    return ['missionId', 'type', 'date'];
  }

  /**
   * Get validation schema for create operations
   */
  protected getCreateSchema(): z.ZodSchema<InsertTransaction> {
    return businessSchemas.transaction;
  }

  /**
   * Get validation schema for update operations
   */
  protected getUpdateSchema(): z.ZodSchema<Partial<InsertTransaction>> {
    return businessSchemas.transaction.partial();
  }
}

/**
 * Factory function to create transaction controller
 */
export function createTransactionController(transactionService: TransactionService): TransactionController {
  return new TransactionController(transactionService);
}
