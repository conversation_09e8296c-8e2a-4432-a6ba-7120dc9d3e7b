import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes.js";
import { setupVite, serveStatic, log } from "./vite.js";
import { config, validateConfig } from "./config/index.js";
import { securityMiddlewareStack } from "./middleware/security.js";
import { requestLogger, logger, gracefulShutdown } from "./utils/logger.js";
import authRoutes from "./routes/auth.js";

// Validate configuration on startup
validateConfig();

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Apply security middleware stack
app.use(securityMiddlewareStack);

// Request logging
app.use(requestLogger);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

// Authentication routes
app.use('/api/auth', authRoutes);

(async () => {
  const server = await registerRoutes(app);

  // Global error handler
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    logger.error('Unhandled error', {
      error: err.message,
      stack: err.stack,
      status,
      url: _req.url,
      method: _req.method,
    });

    res.status(status).json({
      error: config.app.isProduction ? 'Internal Server Error' : message,
      ...(config.app.isDevelopment && { stack: err.stack })
    });
  });

  // Setup Vite in development or serve static files in production
  if (config.app.isDevelopment) {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.app.env,
      version: process.env.npm_package_version || '1.0.0',
    });
  });

  // Start server
  const port = config.app.port;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    logger.info(`🚀 Server running on port ${port}`, {
      environment: config.app.env,
      port,
      timestamp: new Date().toISOString(),
    });
  });

  // Graceful shutdown handling
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
      gracefulShutdown();
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    server.close(() => {
      gracefulShutdown();
      process.exit(0);
    });
  });
})();
