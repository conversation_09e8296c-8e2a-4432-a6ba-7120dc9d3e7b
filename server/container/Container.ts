import { DatabaseStorage } from '../storage.js';
import { TransactionService } from '../services/TransactionService.js';
import { TransactionController } from '../controllers/TransactionController.js';
import { logger } from '../utils/logger.js';

/**
 * Dependency injection container
 * Implements Inversion of Control pattern
 */

/**
 * Service registration interface
 */
interface ServiceRegistration<T = any> {
  factory: () => T;
  singleton: boolean;
  instance?: T;
}

/**
 * Container class for dependency injection
 * Follows Single Responsibility Principle
 */
export class Container {
  private services = new Map<string, ServiceRegistration>();
  private instances = new Map<string, any>();

  /**
   * Register a service with the container
   */
  register<T>(
    name: string,
    factory: () => T,
    options: { singleton?: boolean } = {}
  ): void {
    const { singleton = true } = options;
    
    this.services.set(name, {
      factory,
      singleton,
    });

    logger.debug(`Service registered: ${name}`, { singleton });
  }

  /**
   * Register a singleton service
   */
  singleton<T>(name: string, factory: () => T): void {
    this.register(name, factory, { singleton: true });
  }

  /**
   * Register a transient service (new instance each time)
   */
  transient<T>(name: string, factory: () => T): void {
    this.register(name, factory, { singleton: false });
  }

  /**
   * Resolve a service from the container
   */
  resolve<T>(name: string): T {
    const registration = this.services.get(name);
    
    if (!registration) {
      throw new Error(`Service '${name}' not found in container`);
    }

    // Return singleton instance if exists
    if (registration.singleton && registration.instance) {
      return registration.instance;
    }

    // Create new instance
    try {
      const instance = registration.factory();
      
      // Store singleton instance
      if (registration.singleton) {
        registration.instance = instance;
      }

      logger.debug(`Service resolved: ${name}`);
      return instance;
    } catch (error) {
      logger.error(`Failed to resolve service: ${name}`, { error });
      throw new Error(`Failed to create instance of service '${name}': ${error}`);
    }
  }

  /**
   * Check if service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Clear all services (useful for testing)
   */
  clear(): void {
    this.services.clear();
    this.instances.clear();
  }

  /**
   * Register an existing instance
   */
  instance<T>(name: string, instance: T): void {
    this.services.set(name, {
      factory: () => instance,
      singleton: true,
      instance,
    });
  }
}

/**
 * Service names constants
 * Prevents typos and provides type safety
 */
export const ServiceNames = {
  // Infrastructure
  DATABASE_STORAGE: 'DatabaseStorage',
  
  // Services
  TRANSACTION_SERVICE: 'TransactionService',
  
  // Controllers
  TRANSACTION_CONTROLLER: 'TransactionController',
} as const;

/**
 * Configure and setup the dependency injection container
 */
export function setupContainer(): Container {
  const container = new Container();

  // Register infrastructure services
  container.singleton(ServiceNames.DATABASE_STORAGE, () => {
    return new DatabaseStorage();
  });

  // Register business services
  container.singleton(ServiceNames.TRANSACTION_SERVICE, () => {
    const storage = container.resolve<DatabaseStorage>(ServiceNames.DATABASE_STORAGE);
    return new TransactionService(storage);
  });

  // Register controllers
  container.singleton(ServiceNames.TRANSACTION_CONTROLLER, () => {
    const transactionService = container.resolve<TransactionService>(ServiceNames.TRANSACTION_SERVICE);
    return new TransactionController(transactionService);
  });

  logger.info('Dependency injection container configured', {
    services: container.getRegisteredServices(),
  });

  return container;
}

/**
 * Global container instance
 */
export const container = setupContainer();

/**
 * Helper function to resolve services with type safety
 */
export function resolve<T>(serviceName: string): T {
  return container.resolve<T>(serviceName);
}

/**
 * Decorator for automatic dependency injection (future enhancement)
 */
export function Injectable(serviceName?: string) {
  return function <T extends new (...args: any[]) => {}>(constructor: T) {
    const name = serviceName || constructor.name;
    
    // Register the class in the container
    container.register(name, () => new constructor());
    
    return constructor;
  };
}

/**
 * Decorator for injecting dependencies (future enhancement)
 */
export function Inject(serviceName: string) {
  return function (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
    // Store metadata for dependency injection
    const existingTokens = Reflect.getMetadata('design:paramtypes', target) || [];
    existingTokens[parameterIndex] = serviceName;
    Reflect.defineMetadata('design:paramtypes', existingTokens, target);
  };
}

/**
 * Health check for container
 */
export function containerHealthCheck(): {
  status: 'healthy' | 'unhealthy';
  services: { name: string; status: 'ok' | 'error'; error?: string }[];
} {
  const services = container.getRegisteredServices();
  const serviceStatuses = services.map(serviceName => {
    try {
      container.resolve(serviceName);
      return { name: serviceName, status: 'ok' as const };
    } catch (error) {
      return { 
        name: serviceName, 
        status: 'error' as const, 
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  const hasErrors = serviceStatuses.some(s => s.status === 'error');
  
  return {
    status: hasErrors ? 'unhealthy' : 'healthy',
    services: serviceStatuses,
  };
}
