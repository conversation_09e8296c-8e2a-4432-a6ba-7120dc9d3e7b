import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cors from 'cors';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';

/**
 * Security middleware collection following OWASP guidelines
 * Implements defense in depth strategy
 */

/**
 * Helmet configuration for security headers
 * Protects against common vulnerabilities
 */
export const helmetMiddleware = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for development compatibility
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
});

/**
 * CORS configuration with environment-specific origins
 * Prevents unauthorized cross-origin requests
 */
export const corsMiddleware = cors({
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);

    if (config.security.cors.origin.includes(origin)) {
      return callback(null, true);
    }

    logger.warn('CORS blocked request from origin:', { origin });
    return callback(new Error('Not allowed by CORS'), false);
  },
  credentials: config.security.cors.credentials,
  methods: config.security.cors.methods,
  allowedHeaders: config.security.cors.allowedHeaders,
  maxAge: config.security.cors.maxAge,
});

/**
 * Rate limiting middleware
 * Protects against brute force and DoS attacks
 */
export const rateLimitMiddleware = rateLimit({
  windowMs: config.security.rateLimit.windowMs,
  max: config.security.rateLimit.max,
  message: {
    error: config.security.rateLimit.message,
    retryAfter: Math.ceil(config.security.rateLimit.windowMs / 1000),
  },
  standardHeaders: config.security.rateLimit.standardHeaders,
  legacyHeaders: config.security.rateLimit.legacyHeaders,
  handler: (req: Request, res: Response) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    });
    
    res.status(429).json({
      error: 'Too many requests',
      message: config.security.rateLimit.message,
      retryAfter: Math.ceil(config.security.rateLimit.windowMs / 1000),
    });
  },
});

/**
 * Stricter rate limiting for authentication endpoints
 * Prevents brute force attacks on login
 */
export const authRateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  skipSuccessfulRequests: true,
  message: {
    error: 'Too many authentication attempts',
    message: 'Please try again in 15 minutes',
  },
  handler: (req: Request, res: Response) => {
    logger.warn('Auth rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    });
    
    res.status(429).json({
      error: 'Too many authentication attempts',
      message: 'Please try again in 15 minutes',
    });
  },
});

/**
 * Input sanitization middleware
 * Prevents XSS and injection attacks
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Recursively sanitize object properties
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      // Remove potentially dangerous characters
      return obj
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  
  if (req.params) {
    req.params = sanitizeObject(req.params);
  }

  next();
};

/**
 * Request validation middleware
 * Validates request size and content type
 */
export const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  // Check content length
  const contentLength = parseInt(req.get('content-length') || '0');
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (contentLength > maxSize) {
    logger.warn('Request too large', {
      ip: req.ip,
      contentLength,
      maxSize,
      path: req.path,
    });
    
    return res.status(413).json({
      error: 'Request too large',
      message: 'Request size exceeds maximum allowed limit',
    });
  }

  // Validate content type for POST/PUT requests
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const contentType = req.get('content-type');
    const allowedTypes = [
      'application/json',
      'application/x-www-form-urlencoded',
      'multipart/form-data',
    ];
    
    if (contentType && !allowedTypes.some(type => contentType.includes(type))) {
      logger.warn('Invalid content type', {
        ip: req.ip,
        contentType,
        path: req.path,
      });
      
      return res.status(415).json({
        error: 'Unsupported media type',
        message: 'Content type not supported',
      });
    }
  }

  next();
};

/**
 * Security headers middleware
 * Adds additional security headers
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  // Add custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Add cache control for sensitive endpoints
  if (req.path.startsWith('/api/')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }

  next();
};

/**
 * IP whitelist middleware (optional)
 * Restricts access to specific IP addresses
 */
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP || '')) {
      logger.warn('IP not whitelisted', {
        ip: clientIP,
        path: req.path,
      });
      
      return res.status(403).json({
        error: 'Access denied',
        message: 'Your IP address is not authorized',
      });
    }
    
    next();
  };
};

/**
 * Comprehensive security middleware stack
 * Applies all security measures in correct order
 */
export const securityMiddlewareStack = [
  helmetMiddleware,
  corsMiddleware,
  securityHeaders,
  rateLimitMiddleware,
  validateRequest,
  sanitizeInput,
];

/**
 * Authentication-specific security stack
 * Additional protection for auth endpoints
 */
export const authSecurityStack = [
  ...securityMiddlewareStack,
  authRateLimitMiddleware,
];
