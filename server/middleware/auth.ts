import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { z } from 'zod';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';

/**
 * User roles with hierarchical permissions
 * Following principle of least privilege
 */
export enum UserRole {
  ADMIN = 'admin',
  ACCOUNTANT = 'accountant',
  READONLY = 'readonly',
}

/**
 * Permission levels for different operations
 */
export enum Permission {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  ADMIN = 'admin',
}

/**
 * Role-based permissions mapping
 * Defines what each role can do
 */
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN],
  [UserRole.ACCOUNTANT]: [Permission.READ, Permission.WRITE],
  [UserRole.READONLY]: [Permission.READ],
};

/**
 * User interface for authentication
 */
export interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
}

/**
 * JWT payload interface
 */
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

/**
 * Extended Request interface with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: User;
}

/**
 * Login request validation schema
 */
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

/**
 * User registration schema
 */
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain uppercase, lowercase, number and special character'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.nativeEnum(UserRole).default(UserRole.READONLY),
});

/**
 * Password hashing utility
 * Uses bcrypt with secure salt rounds
 */
export class PasswordService {
  private static readonly SALT_ROUNDS = 12;

  static async hash(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.SALT_ROUNDS);
    } catch (error) {
      logger.error('Password hashing failed', { error });
      throw new Error('Password processing failed');
    }
  }

  static async verify(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      logger.error('Password verification failed', { error });
      return false;
    }
  }
}

/**
 * JWT token service
 * Handles token generation and validation
 */
export class TokenService {
  static generateToken(user: User): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    return jwt.sign(payload, config.security.jwt.secret, {
      expiresIn: config.security.jwt.expiresIn,
      algorithm: config.security.jwt.algorithm,
      issuer: 'webflowmaster',
      audience: 'webflowmaster-users',
    });
  }

  static verifyToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, config.security.jwt.secret, {
        algorithms: [config.security.jwt.algorithm],
        issuer: 'webflowmaster',
        audience: 'webflowmaster-users',
      }) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token expired');
      }
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      }
      throw new Error('Token verification failed');
    }
  }

  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}

/**
 * Authentication middleware
 * Validates JWT tokens and attaches user to request
 */
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'No token provided',
      });
      return;
    }

    const payload = TokenService.verifyToken(token);
    
    // In a real application, you would fetch the user from database
    // For now, we'll create a user object from the token payload
    const user: User = {
      id: payload.userId,
      email: payload.email,
      role: payload.role,
      name: 'User Name', // This should come from database
      isActive: true,
      createdAt: new Date(),
    };

    // Check if user is active
    if (!user.isActive) {
      res.status(401).json({
        error: 'Account disabled',
        message: 'Your account has been disabled',
      });
      return;
    }

    req.user = user;
    next();
  } catch (error) {
    logger.warn('Authentication failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.status(401).json({
      error: 'Authentication failed',
      message: error instanceof Error ? error.message : 'Invalid token',
    });
  }
};

/**
 * Authorization middleware factory
 * Checks if user has required permissions
 */
export const authorize = (requiredPermissions: Permission[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'User not authenticated',
      });
      return;
    }

    const userPermissions = rolePermissions[req.user.role];
    const hasPermission = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      logger.warn('Authorization failed', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredPermissions,
        userPermissions,
        path: req.path,
      });

      res.status(403).json({
        error: 'Insufficient permissions',
        message: 'You do not have permission to perform this action',
      });
      return;
    }

    next();
  };
};

/**
 * Role-based authorization shortcuts
 */
export const requireAdmin = authorize([Permission.ADMIN]);
export const requireWrite = authorize([Permission.WRITE]);
export const requireRead = authorize([Permission.READ]);

/**
 * Optional authentication middleware
 * Attaches user if token is valid, but doesn't require it
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const payload = TokenService.verifyToken(token);
      
      // Create user object from token payload
      req.user = {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
        name: 'User Name',
        isActive: true,
        createdAt: new Date(),
      };
    }
  } catch (error) {
    // Silently ignore authentication errors for optional auth
    logger.debug('Optional authentication failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }

  next();
};

/**
 * Validation schemas export
 */
export const authSchemas = {
  login: loginSchema,
  register: registerSchema,
};

/**
 * Helper function to check if user has specific permission
 */
export const hasPermission = (user: User, permission: Permission): boolean => {
  return rolePermissions[user.role].includes(permission);
};

/**
 * Helper function to check if user has any of the specified roles
 */
export const hasRole = (user: User, roles: UserRole[]): boolean => {
  return roles.includes(user.role);
};
