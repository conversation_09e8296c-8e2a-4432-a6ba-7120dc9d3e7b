import { Request, Response, NextFunction } from 'express';
import { logger, performanceLogger } from '../utils/logger.js';
import { config } from '../config/index.js';

/**
 * Monitoring and observability middleware
 * Implements comprehensive application monitoring
 */

/**
 * Application metrics interface
 */
interface AppMetrics {
  requests: {
    total: number;
    success: number;
    errors: number;
    byMethod: Record<string, number>;
    byStatus: Record<number, number>;
  };
  performance: {
    averageResponseTime: number;
    slowRequests: number;
    totalResponseTime: number;
  };
  system: {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: NodeJS.CpuUsage;
  };
  database: {
    connections: number;
    queries: number;
    slowQueries: number;
  };
}

/**
 * Metrics collector class
 */
class MetricsCollector {
  private metrics: AppMetrics = {
    requests: {
      total: 0,
      success: 0,
      errors: 0,
      byMethod: {},
      byStatus: {},
    },
    performance: {
      averageResponseTime: 0,
      slowRequests: 0,
      totalResponseTime: 0,
    },
    system: {
      uptime: 0,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
    },
    database: {
      connections: 0,
      queries: 0,
      slowQueries: 0,
    },
  };

  private startTime = Date.now();

  /**
   * Record request metrics
   */
  recordRequest(method: string, statusCode: number, responseTime: number): void {
    this.metrics.requests.total++;
    
    // Count by method
    this.metrics.requests.byMethod[method] = (this.metrics.requests.byMethod[method] || 0) + 1;
    
    // Count by status
    this.metrics.requests.byStatus[statusCode] = (this.metrics.requests.byStatus[statusCode] || 0) + 1;
    
    // Count success/errors
    if (statusCode >= 200 && statusCode < 400) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.errors++;
    }

    // Record performance metrics
    this.metrics.performance.totalResponseTime += responseTime;
    this.metrics.performance.averageResponseTime = 
      this.metrics.performance.totalResponseTime / this.metrics.requests.total;

    // Count slow requests (>1000ms)
    if (responseTime > 1000) {
      this.metrics.performance.slowRequests++;
    }
  }

  /**
   * Record database query
   */
  recordDatabaseQuery(duration: number): void {
    this.metrics.database.queries++;
    
    // Count slow queries (>100ms)
    if (duration > 100) {
      this.metrics.database.slowQueries++;
    }
  }

  /**
   * Update system metrics
   */
  updateSystemMetrics(): void {
    this.metrics.system.uptime = Date.now() - this.startTime;
    this.metrics.system.memoryUsage = process.memoryUsage();
    this.metrics.system.cpuUsage = process.cpuUsage();
  }

  /**
   * Get current metrics
   */
  getMetrics(): AppMetrics {
    this.updateSystemMetrics();
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  reset(): void {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        byMethod: {},
        byStatus: {},
      },
      performance: {
        averageResponseTime: 0,
        slowRequests: 0,
        totalResponseTime: 0,
      },
      system: {
        uptime: 0,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
      },
      database: {
        connections: 0,
        queries: 0,
        slowQueries: 0,
      },
    };
    this.startTime = Date.now();
  }
}

/**
 * Global metrics collector instance
 */
export const metricsCollector = new MetricsCollector();

/**
 * Request monitoring middleware
 * Tracks request metrics and performance
 */
export const requestMonitoring = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const originalSend = res.send;

  // Override res.send to capture response
  res.send = function(body) {
    const responseTime = Date.now() - startTime;
    
    // Record metrics
    metricsCollector.recordRequest(req.method, res.statusCode, responseTime);
    
    // Log slow requests
    if (responseTime > 1000) {
      performanceLogger.slowRequest(req.method, req.url, responseTime);
    }

    // Log request completion
    logger.debug('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    return originalSend.call(this, body);
  };

  next();
};

/**
 * Health check endpoint handler
 */
export const healthCheck = async (req: Request, res: Response): Promise<void> => {
  try {
    const metrics = metricsCollector.getMetrics();
    const health = await performHealthChecks();

    const healthStatus = {
      status: health.overall,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.app.env,
      checks: health.checks,
      metrics: {
        requests: metrics.requests.total,
        errors: metrics.requests.errors,
        averageResponseTime: Math.round(metrics.performance.averageResponseTime),
        memoryUsage: Math.round(metrics.system.memoryUsage.heapUsed / 1024 / 1024), // MB
      },
    };

    const statusCode = health.overall === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthStatus);
  } catch (error) {
    logger.error('Health check failed', { error });
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
};

/**
 * Metrics endpoint handler
 */
export const metricsEndpoint = (req: Request, res: Response): void => {
  if (!config.monitoring.enabled) {
    res.status(404).json({ error: 'Metrics disabled' });
    return;
  }

  const metrics = metricsCollector.getMetrics();
  res.json(metrics);
};

/**
 * Perform health checks
 */
async function performHealthChecks(): Promise<{
  overall: 'healthy' | 'unhealthy';
  checks: Record<string, { status: 'ok' | 'error'; message?: string; responseTime?: number }>;
}> {
  const checks: Record<string, any> = {};

  // Database health check
  try {
    const dbStart = Date.now();
    // In a real implementation, you would ping the database
    // await db.raw('SELECT 1');
    checks.database = {
      status: 'ok',
      responseTime: Date.now() - dbStart,
    };
  } catch (error) {
    checks.database = {
      status: 'error',
      message: error instanceof Error ? error.message : 'Database connection failed',
    };
  }

  // Memory health check
  const memoryUsage = process.memoryUsage();
  const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
  
  checks.memory = {
    status: memoryUsagePercent < 90 ? 'ok' : 'error',
    message: `Memory usage: ${Math.round(memoryUsagePercent)}%`,
  };

  // Disk space health check (simplified)
  checks.disk = {
    status: 'ok',
    message: 'Disk space check not implemented',
  };

  // External services health check
  checks.externalServices = {
    status: 'ok',
    message: 'No external services configured',
  };

  // Determine overall health
  const hasErrors = Object.values(checks).some((check: any) => check.status === 'error');
  const overall = hasErrors ? 'unhealthy' : 'healthy';

  return { overall, checks };
}

/**
 * Performance monitoring middleware
 * Monitors and logs performance metrics
 */
export const performanceMonitoring = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

    // Log performance metrics
    logger.debug('Request performance', {
      method: req.method,
      url: req.url,
      duration,
      memoryDelta,
      statusCode: res.statusCode,
    });

    // Alert on performance issues
    if (duration > 5000) { // 5 seconds
      performanceLogger.slowRequest(req.method, req.url, duration);
    }

    if (memoryDelta > 10 * 1024 * 1024) { // 10MB
      logger.warn('High memory usage detected', {
        method: req.method,
        url: req.url,
        memoryDelta: Math.round(memoryDelta / 1024 / 1024), // MB
      });
    }
  });

  next();
};

/**
 * Error rate monitoring
 */
export const errorRateMonitoring = (): void => {
  setInterval(() => {
    const metrics = metricsCollector.getMetrics();
    const errorRate = metrics.requests.total > 0 
      ? (metrics.requests.errors / metrics.requests.total) * 100 
      : 0;

    if (errorRate > 10) { // 10% error rate threshold
      logger.warn('High error rate detected', {
        errorRate: Math.round(errorRate * 100) / 100,
        totalRequests: metrics.requests.total,
        errors: metrics.requests.errors,
      });
    }
  }, 60000); // Check every minute
};

/**
 * Memory monitoring
 */
export const memoryMonitoring = (): void => {
  setInterval(() => {
    performanceLogger.memoryUsage();
    
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    if (memoryUsagePercent > 85) {
      logger.warn('High memory usage', {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        percentage: Math.round(memoryUsagePercent),
      });
    }
  }, 30000); // Check every 30 seconds
};

/**
 * Start monitoring services
 */
export function startMonitoring(): void {
  if (config.monitoring.enabled) {
    errorRateMonitoring();
    memoryMonitoring();
    
    logger.info('Monitoring services started');
  }
}
