# Configuration Analysis for Neon to Supabase Migration

## Current Configuration Issues

### 1. Database URL Inconsistencies

**Current .env file**:
```
DATABASE_URL=postgresql://postgres:postgresql@localhost:5432/db-finanzas
```

**Docker Compose Configuration**:
```yaml
ports:
  - "5433:5432"
environment:
  POSTGRES_DB: webflowmaster
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgresql
```

**Memory Configuration**:
- Host: localhost:5433
- User: postgres
- Password: postgresql
- Database: (not specified, likely webflowmaster)

### 2. Identified Inconsistencies

| Component | Host | Port | Database | User | Password |
|-----------|------|------|----------|------|----------|
| .env | localhost | 5432 | db-finanzas | postgres | postgresql |
| Docker | localhost | 5433 | webflowmaster | postgres | postgresql |
| Memory | localhost | 5433 | ? | postgres | postgresql |

### 3. Neon-Specific Dependencies

**Current packages to replace**:
- `@neondatabase/serverless`: Neon-specific serverless driver
- WebSocket configuration in `server/db.ts`

**Files requiring updates**:
- `server/db.ts`: Database connection setup
- `package.json`: Dependencies
- `drizzle.config.ts`: ORM configuration

## Recommended Supabase Configuration

### 1. Standardized Database Configuration

**Proposed DATABASE_URL** (assuming self-hosted Supabase on default port):
```
DATABASE_URL=postgresql://postgres:postgresql@localhost:5432/webflowmaster
```

**Alternative for custom Supabase setup**:
```
DATABASE_URL=postgresql://postgres:postgresql@localhost:54322/postgres
```
*Note: Supabase typically uses port 54322 for PostgreSQL and database name 'postgres'*

### 2. Additional Supabase Environment Variables

```env
# Supabase Configuration
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database Configuration (updated)
DATABASE_URL=postgresql://postgres:postgresql@localhost:54322/postgres
```

### 3. Package Dependencies Changes

**Remove**:
```json
"@neondatabase/serverless": "^0.10.4"
```

**Add**:
```json
"@supabase/supabase-js": "^2.39.0",
"postgres": "^3.4.3"
```

**Update Drizzle adapter**:
```json
// Change from: drizzle-orm/neon-serverless
// To: drizzle-orm/postgres-js
```

## Migration Steps for Phase 2

### Step 1: Backup Current Configuration
```bash
cp .env .env.backup
cp package.json package.json.backup
```

### Step 2: Update .env File
- Resolve database name inconsistency (use 'webflowmaster')
- Update port to match Docker setup (5433) or Supabase setup
- Add Supabase-specific environment variables

### Step 3: Update Package Dependencies
- Remove Neon packages
- Add Supabase and standard PostgreSQL packages
- Update Drizzle ORM adapter

### Step 4: Verify Self-Hosted Supabase Configuration
- Confirm Supabase instance is running
- Verify connection details (host, port, database, credentials)
- Test basic connectivity

## Questions for User

1. **Supabase Instance Details**:
   - What port is your self-hosted Supabase PostgreSQL running on?
   - What is the database name in your Supabase setup?
   - Do you have the Supabase API URL and keys?

2. **Database Name Preference**:
   - Should we use 'webflowmaster' or 'db-finanzas' as the database name?
   - Do you want to keep the existing data or start fresh?

3. **Authentication Strategy**:
   - Keep current JWT authentication or migrate to Supabase Auth?
   - Do you want to use Supabase's built-in authentication features?

## Next Actions

1. Clarify Supabase instance configuration details
2. Update .env file with correct Supabase settings
3. Update package.json dependencies
4. Test basic connectivity before proceeding to code changes
