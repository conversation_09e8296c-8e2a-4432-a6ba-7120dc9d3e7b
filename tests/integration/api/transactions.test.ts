import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { TransactionController } from '@server/controllers/TransactionController';
import { TransactionService } from '@server/services/TransactionService';
import { globalErrorHandler } from '@server/middleware/errorHandler';
import { createMockTransaction, createMockUser } from '@tests/utils/testHelpers';

/**
 * Integration tests for Transaction API endpoints
 * Tests the full request/response cycle
 */

// Mock dependencies
vi.mock('@server/services/TransactionService');
vi.mock('@server/utils/logger');
vi.mock('@server/middleware/auth');

describe('Transaction API Integration Tests', () => {
  let app: express.Application;
  let mockTransactionService: TransactionService;
  let transactionController: TransactionController;

  beforeEach(() => {
    // Create mock service
    mockTransactionService = {
      findAll: vi.fn(),
      findByMission: vi.fn(),
      createTransaction: vi.fn(),
      safeDelete: vi.fn(),
      canDelete: vi.fn(),
      getCurrentBalance: vi.fn(),
      getTotalSavings: vi.fn(),
      getBalanceHistory: vi.fn(),
      getBalanceAnalytics: vi.fn(),
    } as any;

    // Create controller
    transactionController = new TransactionController(mockTransactionService);

    // Setup Express app
    app = express();
    app.use(express.json());

    // Setup routes
    app.get('/api/transactions', (req, res) => transactionController.index(req, res));
    app.post('/api/transactions', (req, res) => transactionController.create(req, res));
    app.delete('/api/transactions/:id', (req, res) => transactionController.destroy(req, res));
    app.get('/api/transactions/analytics/balance', (req, res) => 
      transactionController.getBalanceAnalytics(req, res));
    app.get('/api/transactions/analytics/balance-history', (req, res) => 
      transactionController.getBalanceHistory(req, res));
    app.get('/api/transactions/:id/can-delete', (req, res) => 
      transactionController.canDelete(req, res));

    // Error handler
    app.use(globalErrorHandler);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('GET /api/transactions', () => {
    it('should return all transactions', async () => {
      // Arrange
      const transactions = [
        createMockTransaction({ id: 1 }),
        createMockTransaction({ id: 2 }),
      ];
      mockTransactionService.findAll = vi.fn().mockResolvedValue(transactions);

      // Act
      const response = await request(app)
        .get('/api/transactions')
        .expect(200);

      // Assert
      expect(response.body.data).toEqual(transactions);
      expect(mockTransactionService.findAll).toHaveBeenCalledOnce();
    });

    it('should return transactions filtered by mission', async () => {
      // Arrange
      const missionId = 1;
      const transactions = [createMockTransaction({ id: 1, missionId })];
      mockTransactionService.findByMission = vi.fn().mockResolvedValue(transactions);

      // Act
      const response = await request(app)
        .get(`/api/transactions?missionId=${missionId}`)
        .expect(200);

      // Assert
      expect(response.body.data).toEqual(transactions);
      expect(mockTransactionService.findByMission).toHaveBeenCalledWith(missionId);
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      mockTransactionService.findAll = vi.fn().mockRejectedValue(new Error('Database error'));

      // Act
      const response = await request(app)
        .get('/api/transactions')
        .expect(500);

      // Assert
      expect(response.body.error).toBe('Internal Server Error');
    });
  });

  describe('POST /api/transactions', () => {
    it('should create a new transaction', async () => {
      // Arrange
      const transactionData = {
        date: '2024-01',
        type: 'savings',
        amount: 1000,
        missionId: 1,
      };
      const createdTransaction = createMockTransaction(transactionData);
      mockTransactionService.createTransaction = vi.fn().mockResolvedValue(createdTransaction);

      // Act
      const response = await request(app)
        .post('/api/transactions')
        .send(transactionData)
        .expect(201);

      // Assert
      expect(response.body.data).toEqual(createdTransaction);
      expect(response.body.message).toBe('Transaction created successfully');
      expect(mockTransactionService.createTransaction).toHaveBeenCalledWith(transactionData);
    });

    it('should validate transaction data', async () => {
      // Arrange
      const invalidData = {
        date: 'invalid-date',
        type: 'invalid-type',
        amount: -100,
      };

      // Act
      const response = await request(app)
        .post('/api/transactions')
        .send(invalidData)
        .expect(400);

      // Assert
      expect(response.body.error).toBe('Validation failed');
      expect(response.body.details).toBeDefined();
    });

    it('should handle missing required fields', async () => {
      // Arrange
      const incompleteData = {
        date: '2024-01',
        // missing type and amount
      };

      // Act
      const response = await request(app)
        .post('/api/transactions')
        .send(incompleteData)
        .expect(400);

      // Assert
      expect(response.body.error).toBe('Validation failed');
    });
  });

  describe('DELETE /api/transactions/:id', () => {
    it('should delete a transaction', async () => {
      // Arrange
      const transactionId = 1;
      mockTransactionService.safeDelete = vi.fn().mockResolvedValue(undefined);

      // Act
      const response = await request(app)
        .delete(`/api/transactions/${transactionId}`)
        .expect(204);

      // Assert
      expect(response.body).toEqual({});
      expect(mockTransactionService.safeDelete).toHaveBeenCalledWith(transactionId);
    });

    it('should handle deletion of non-existent transaction', async () => {
      // Arrange
      const transactionId = 999;
      mockTransactionService.safeDelete = vi.fn()
        .mockRejectedValue(new Error('Transaction not found'));

      // Act
      const response = await request(app)
        .delete(`/api/transactions/${transactionId}`)
        .expect(500);

      // Assert
      expect(response.body.error).toBe('Internal Server Error');
    });

    it('should handle deletion restrictions', async () => {
      // Arrange
      const transactionId = 1;
      mockTransactionService.safeDelete = vi.fn()
        .mockRejectedValue(new Error('Cannot delete transaction older than one month'));

      // Act
      const response = await request(app)
        .delete(`/api/transactions/${transactionId}`)
        .expect(500);

      // Assert
      expect(response.body.error).toBe('Internal Server Error');
    });
  });

  describe('GET /api/transactions/analytics/balance', () => {
    it('should return balance analytics', async () => {
      // Arrange
      const analytics = {
        currentBalance: 5000,
        totalSavings: 10000,
        halfBalance: 2500,
      };
      mockTransactionService.getBalanceAnalytics = vi.fn().mockResolvedValue(analytics);

      // Act
      const response = await request(app)
        .get('/api/transactions/analytics/balance')
        .expect(200);

      // Assert
      expect(response.body.data).toEqual(analytics);
      expect(mockTransactionService.getBalanceAnalytics).toHaveBeenCalledOnce();
    });
  });

  describe('GET /api/transactions/analytics/balance-history', () => {
    it('should return balance history with default months', async () => {
      // Arrange
      const history = [
        { date: '2024-01', balance: 1000 },
        { date: '2024-02', balance: 1500 },
      ];
      mockTransactionService.getBalanceHistory = vi.fn().mockResolvedValue(history);

      // Act
      const response = await request(app)
        .get('/api/transactions/analytics/balance-history')
        .expect(200);

      // Assert
      expect(response.body.data).toEqual(history);
      expect(mockTransactionService.getBalanceHistory).toHaveBeenCalledWith(12);
    });

    it('should return balance history with custom months', async () => {
      // Arrange
      const months = 6;
      const history = [{ date: '2024-01', balance: 1000 }];
      mockTransactionService.getBalanceHistory = vi.fn().mockResolvedValue(history);

      // Act
      const response = await request(app)
        .get(`/api/transactions/analytics/balance-history?months=${months}`)
        .expect(200);

      // Assert
      expect(response.body.data).toEqual(history);
      expect(mockTransactionService.getBalanceHistory).toHaveBeenCalledWith(months);
    });
  });

  describe('GET /api/transactions/:id/can-delete', () => {
    it('should return true if transaction can be deleted', async () => {
      // Arrange
      const transactionId = 1;
      mockTransactionService.canDelete = vi.fn().mockResolvedValue(true);

      // Act
      const response = await request(app)
        .get(`/api/transactions/${transactionId}/can-delete`)
        .expect(200);

      // Assert
      expect(response.body.data.canDelete).toBe(true);
      expect(response.body.message).toBe('Transaction can be deleted');
      expect(mockTransactionService.canDelete).toHaveBeenCalledWith(transactionId);
    });

    it('should return false if transaction cannot be deleted', async () => {
      // Arrange
      const transactionId = 1;
      mockTransactionService.canDelete = vi.fn().mockResolvedValue(false);

      // Act
      const response = await request(app)
        .get(`/api/transactions/${transactionId}/can-delete`)
        .expect(200);

      // Assert
      expect(response.body.data.canDelete).toBe(false);
      expect(response.body.message).toBe('Transaction cannot be deleted');
    });
  });
});
