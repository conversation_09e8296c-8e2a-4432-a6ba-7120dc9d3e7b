import { test, expect } from '@playwright/test';

/**
 * End-to-end tests for the dashboard functionality
 * Tests user workflows and interactions
 */

test.describe('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
  });

  test('should display dashboard with key metrics', async ({ page }) => {
    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Check if the dashboard title is visible
    await expect(page.locator('h1')).toContainText('Dashboard');

    // Check for key metric cards
    await expect(page.locator('[data-testid="current-balance"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-savings"]')).toBeVisible();
    
    // Check for charts
    await expect(page.locator('[data-testid="balance-chart"]')).toBeVisible();
  });

  test('should navigate to transactions page', async ({ page }) => {
    // Click on transactions navigation link
    await page.click('[data-testid="nav-transactions"]');

    // Verify we're on the transactions page
    await expect(page).toHaveURL('/transactions');
    await expect(page.locator('h1')).toContainText('Transactions');
  });

  test('should navigate to expenses page', async ({ page }) => {
    // Click on expenses navigation link
    await page.click('[data-testid="nav-expenses"]');

    // Verify we're on the expenses page
    await expect(page).toHaveURL('/expenses');
    await expect(page.locator('h1')).toContainText('Expenses');
  });

  test('should navigate to payroll page', async ({ page }) => {
    // Click on payroll navigation link
    await page.click('[data-testid="nav-payroll"]');

    // Verify we're on the payroll page
    await expect(page).toHaveURL('/payroll');
    await expect(page.locator('h1')).toContainText('Payroll');
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Check if mobile navigation is visible
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible();
    
    // Check if desktop sidebar is hidden
    await expect(page.locator('[data-testid="desktop-sidebar"]')).toBeHidden();
  });

  test('should display recent activity', async ({ page }) => {
    // Wait for data to load
    await page.waitForLoadState('networkidle');

    // Check for recent activity section
    await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
    
    // Check if activity items are displayed
    const activityItems = page.locator('[data-testid="activity-item"]');
    await expect(activityItems.first()).toBeVisible();
  });
});

test.describe('Dashboard Interactions', () => {
  test('should refresh data when refresh button is clicked', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Click refresh button
    await page.click('[data-testid="refresh-button"]');

    // Wait for loading indicator
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible();
    
    // Wait for data to reload
    await page.waitForLoadState('networkidle');
    
    // Verify loading indicator is gone
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeHidden();
  });

  test('should filter data by date range', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Open date filter
    await page.click('[data-testid="date-filter"]');

    // Select last 30 days
    await page.click('[data-testid="filter-30-days"]');

    // Wait for data to update
    await page.waitForLoadState('networkidle');

    // Verify filter is applied
    await expect(page.locator('[data-testid="active-filter"]')).toContainText('Last 30 days');
  });
});

test.describe('Dashboard Error Handling', () => {
  test('should display error message when API fails', async ({ page }) => {
    // Mock API failure
    await page.route('**/api/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' }),
      });
    });

    await page.goto('/');

    // Check for error message
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Failed to load data');
  });

  test('should retry failed requests', async ({ page }) => {
    let requestCount = 0;
    
    // Mock API to fail first request, succeed on retry
    await page.route('**/api/analytics/balance', route => {
      requestCount++;
      if (requestCount === 1) {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' }),
        });
      } else {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            data: {
              currentBalance: 5000,
              totalSavings: 10000,
              halfBalance: 2500,
            },
          }),
        });
      }
    });

    await page.goto('/');

    // Click retry button
    await page.click('[data-testid="retry-button"]');

    // Wait for successful data load
    await page.waitForLoadState('networkidle');

    // Verify data is displayed
    await expect(page.locator('[data-testid="current-balance"]')).toContainText('5,000');
  });
});

test.describe('Dashboard Accessibility', () => {
  test('should be accessible with keyboard navigation', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test tab navigation
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toBeVisible();

    // Navigate through main elements
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      await expect(page.locator(':focus')).toBeVisible();
    }
  });

  test('should have proper ARIA labels', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check for ARIA labels on interactive elements
    await expect(page.locator('[data-testid="nav-transactions"]')).toHaveAttribute('aria-label');
    await expect(page.locator('[data-testid="refresh-button"]')).toHaveAttribute('aria-label');
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toHaveCount(1);
    
    const h2 = page.locator('h2');
    await expect(h2.first()).toBeVisible();
  });
});
