# 🚀 WebFlowMaster db-finanzas Setup Instructions

## Overview

Your WebFlowMaster project has been successfully updated to use the "db-finanzas" database name and configured for self-hosted Supabase. This guide provides step-by-step instructions to complete the setup.

## ✅ What's Already Done

1. **Database Configuration Updated**: `.env` file now points to `db-finanzas` database
2. **Supabase Connection Settings**: Updated to use standard Supabase ports (54322 for PostgreSQL, 54321 for API)
3. **Migration Scripts Created**: Automated scripts for setup and data migration
4. **Verification Tools**: Comprehensive testing scripts to validate the setup

## 📋 Current Configuration

```env
DATABASE_URL=postgresql://postgres:postgresql@localhost:54322/db-finanzas
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🛠️ Setup Steps

### Step 1: Verify Supabase Instance

First, check if your Supabase instance exists and is properly configured:

```bash
# Check if Supabase directory exists
ls -la /home/<USER>/GithubProjects/supabase/

# If it doesn't exist, you'll need to set up Supabase first
```

### Step 2: Set Up Supabase (if needed)

If you don't have a Supabase instance yet, set it up:

```bash
# Navigate to your projects directory
cd /home/<USER>/GithubProjects/

# Clone Supabase
git clone https://github.com/supabase/supabase.git

# Navigate to Docker setup
cd supabase/docker

# Copy environment file
cp .env.example .env

# Start Supabase
docker-compose up -d
```

### Step 3: Configure Supabase for db-finanzas

Run the automated configuration script:

```bash
# Navigate to WebFlowMaster directory
cd /home/<USER>/GithubProjects/WebFlowMaster/

# Run the Supabase configuration script
./configure-supabase.sh
```

This script will:
- ✅ Check if Supabase is running
- ✅ Start Supabase if needed
- ✅ Create the "db-finanzas" database
- ✅ Get API keys from your Supabase instance
- ✅ Update WebFlowMaster configuration
- ✅ Test connectivity

### Step 4: Migrate Data (if you have existing data)

If you have existing data in the "webflowmaster" database:

```bash
# Run the data migration script
./migrate-to-db-finanzas.sh
```

This script will:
- ✅ Backup existing data
- ✅ Create the new database
- ✅ Migrate schema and data
- ✅ Verify migration success

### Step 5: Create Fresh Schema (if no existing data)

If you're starting fresh or don't have existing data:

```bash
# Run Drizzle migration to create tables
npx drizzle-kit push
```

### Step 6: Verify Setup

Run the comprehensive verification script:

```bash
# Test all aspects of the setup
node verify-db-finanzas-setup.js
```

This will test:
- ✅ Environment variables
- ✅ Database connectivity
- ✅ Schema verification
- ✅ Drizzle ORM integration
- ✅ Write permissions
- ✅ Supabase API (optional)
- ✅ Performance

### Step 7: Test Application

Start your application to ensure everything works:

```bash
# Start in development mode
npm run dev

# The application should start on http://localhost:5000
```

## 🔧 Manual Setup (Alternative)

If the automated scripts don't work, you can set up manually:

### 1. Start Supabase Manually

```bash
cd /home/<USER>/GithubProjects/supabase/docker
docker-compose up -d
```

### 2. Create Database Manually

```bash
# Connect to PostgreSQL
psql -h localhost -p 54322 -U postgres

# Create database
CREATE DATABASE "db-finanzas";
GRANT ALL PRIVILEGES ON DATABASE "db-finanzas" TO postgres;
\q
```

### 3. Get API Keys

Access Supabase Studio at http://localhost:54323 and get your API keys from Settings → API.

### 4. Update .env File

Update your `.env` file with the actual API keys from your Supabase instance.

## 🔍 Troubleshooting

### Common Issues

#### 1. "Tenant or user not found"
- **Cause**: Supabase is not running or database doesn't exist
- **Solution**: Run `./configure-supabase.sh` or start Supabase manually

#### 2. "Connection refused"
- **Cause**: PostgreSQL is not accessible on port 54322
- **Solution**: Check if Supabase is running: `docker ps | grep supabase`

#### 3. "Database does not exist"
- **Cause**: The "db-finanzas" database hasn't been created
- **Solution**: Run the configuration script or create manually

#### 4. "Permission denied"
- **Cause**: PostgreSQL user doesn't have proper permissions
- **Solution**: Grant permissions or check user configuration

### Verification Commands

```bash
# Check if Supabase is running
docker ps | grep supabase

# Test PostgreSQL connection
psql -h localhost -p 54322 -U postgres -l

# Test Supabase API
curl http://localhost:54321/health

# Test WebFlowMaster connectivity
node test-connectivity.js
```

## 📁 Files Created/Updated

### Updated Files
- `.env` - Database URL updated to use db-finanzas
- `test-connectivity.js` - Updated for new database name

### New Files
- `SUPABASE_CONFIGURATION_GUIDE.md` - Detailed setup guide
- `configure-supabase.sh` - Automated Supabase configuration
- `migrate-to-db-finanzas.sh` - Data migration script
- `verify-db-finanzas-setup.js` - Comprehensive verification
- `SETUP_INSTRUCTIONS.md` - This file

## 🎯 Success Criteria

Your setup is complete when:
- ✅ `node verify-db-finanzas-setup.js` passes all tests
- ✅ `npm run dev` starts without errors
- ✅ Application connects to db-finanzas database
- ✅ All CRUD operations work correctly

## 📞 Next Steps

1. **Run the setup**: `./configure-supabase.sh`
2. **Verify everything**: `node verify-db-finanzas-setup.js`
3. **Test the app**: `npm run dev`
4. **Access Supabase Studio**: http://localhost:54323
5. **Deploy when ready**: Update production configuration

## 🔗 Useful Links

- **Supabase Studio**: http://localhost:54323
- **Supabase API**: http://localhost:54321
- **WebFlowMaster App**: http://localhost:5000 (when running)

---

**Your WebFlowMaster project is now configured for db-finanzas with self-hosted Supabase!** 🎉

Run `./configure-supabase.sh` to get started.
