#!/bin/bash

# Database Migration Script: webflowmaster → db-finanzas
# Handles data migration when changing database names

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
OLD_DB="webflowmaster"
NEW_DB="db-finanzas"
POSTGRES_HOST="localhost"
POSTGRES_PORT="54322"
POSTGRES_USER="postgres"
BACKUP_DIR="db-migration-backup/$(date +%Y%m%d_%H%M%S)"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if PostgreSQL is accessible
check_postgres() {
    log "Checking PostgreSQL connectivity..."
    
    if pg_isready -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER &> /dev/null; then
        success "PostgreSQL is accessible"
    else
        error "Cannot connect to PostgreSQL at $POSTGRES_HOST:$POSTGRES_PORT"
        echo "Please ensure your Supabase instance is running"
        exit 1
    fi
}

# Check if databases exist
check_databases() {
    log "Checking database existence..."
    
    # Check if old database exists
    if psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -lqt | cut -d \| -f 1 | grep -qw "$OLD_DB"; then
        success "Source database '$OLD_DB' exists"
        OLD_DB_EXISTS=true
    else
        warning "Source database '$OLD_DB' does not exist"
        OLD_DB_EXISTS=false
    fi
    
    # Check if new database exists
    if psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -lqt | cut -d \| -f 1 | grep -qw "$NEW_DB"; then
        success "Target database '$NEW_DB' exists"
        NEW_DB_EXISTS=true
    else
        warning "Target database '$NEW_DB' does not exist"
        NEW_DB_EXISTS=false
    fi
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory..."
    mkdir -p "$BACKUP_DIR"
    success "Backup directory created: $BACKUP_DIR"
}

# Backup existing data
backup_data() {
    if [ "$OLD_DB_EXISTS" = true ]; then
        log "Backing up data from '$OLD_DB'..."
        
        # Create schema backup
        pg_dump -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
                --schema-only --no-owner --no-privileges \
                "$OLD_DB" > "$BACKUP_DIR/${OLD_DB}_schema.sql"
        success "Schema backup created"
        
        # Create data backup
        pg_dump -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
                --data-only --no-owner --no-privileges \
                "$OLD_DB" > "$BACKUP_DIR/${OLD_DB}_data.sql"
        success "Data backup created"
        
        # Create complete backup
        pg_dump -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
                --no-owner --no-privileges \
                "$OLD_DB" > "$BACKUP_DIR/${OLD_DB}_complete.sql"
        success "Complete backup created"
        
        # Get table count for verification
        TABLE_COUNT=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d "$OLD_DB" \
                     -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
        log "Source database has $TABLE_COUNT tables"
        
    else
        warning "No data to backup - source database does not exist"
    fi
}

# Create target database
create_target_database() {
    if [ "$NEW_DB_EXISTS" = false ]; then
        log "Creating target database '$NEW_DB'..."
        
        psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
             -c "CREATE DATABASE \"$NEW_DB\";"
        success "Database '$NEW_DB' created"
        
        # Grant permissions
        psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
             -c "GRANT ALL PRIVILEGES ON DATABASE \"$NEW_DB\" TO $POSTGRES_USER;"
        success "Permissions granted"
        
    else
        log "Target database '$NEW_DB' already exists"
        
        # Check if it has tables
        TABLE_COUNT=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d "$NEW_DB" \
                     -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
        
        if [ "$TABLE_COUNT" -gt 0 ]; then
            warning "Target database already has $TABLE_COUNT tables"
            echo ""
            read -p "Do you want to continue? This may overwrite existing data. (y/n): " -n 1 -r
            echo ""
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                error "Migration cancelled by user"
                exit 1
            fi
        fi
    fi
}

# Migrate data
migrate_data() {
    if [ "$OLD_DB_EXISTS" = true ]; then
        log "Migrating data from '$OLD_DB' to '$NEW_DB'..."
        
        # Restore schema first
        log "Restoring schema..."
        psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
             -d "$NEW_DB" < "$BACKUP_DIR/${OLD_DB}_schema.sql" &> /dev/null
        success "Schema restored"
        
        # Restore data
        log "Restoring data..."
        psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
             -d "$NEW_DB" < "$BACKUP_DIR/${OLD_DB}_data.sql" &> /dev/null
        success "Data restored"
        
        # Verify migration
        NEW_TABLE_COUNT=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d "$NEW_DB" \
                         -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
        
        if [ "$NEW_TABLE_COUNT" -eq "$TABLE_COUNT" ]; then
            success "Migration verified - $NEW_TABLE_COUNT tables migrated"
        else
            warning "Table count mismatch: expected $TABLE_COUNT, got $NEW_TABLE_COUNT"
        fi
        
    else
        log "No data to migrate - will use fresh schema"
        
        # Run Drizzle migration to create fresh schema
        log "Creating fresh schema using Drizzle..."
        if npx drizzle-kit push --config=drizzle.config.ts; then
            success "Fresh schema created using Drizzle"
        else
            error "Failed to create schema using Drizzle"
            exit 1
        fi
    fi
}

# Test new database
test_new_database() {
    log "Testing new database connection..."
    
    # Update DATABASE_URL temporarily for testing
    export DATABASE_URL="postgresql://$POSTGRES_USER:postgresql@$POSTGRES_HOST:$POSTGRES_PORT/$NEW_DB"
    
    # Test connectivity
    if node test-connectivity.js &> /dev/null; then
        success "New database connectivity test passed"
    else
        error "New database connectivity test failed"
        return 1
    fi
    
    # Test basic operations
    log "Testing basic database operations..."
    if psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d "$NEW_DB" \
            -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" &> /dev/null; then
        success "Basic database operations working"
    else
        error "Basic database operations failed"
        return 1
    fi
}

# Cleanup old database (optional)
cleanup_old_database() {
    if [ "$OLD_DB_EXISTS" = true ]; then
        echo ""
        read -p "Do you want to remove the old database '$OLD_DB'? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log "Removing old database '$OLD_DB'..."
            psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER \
                 -c "DROP DATABASE \"$OLD_DB\";"
            success "Old database '$OLD_DB' removed"
        else
            log "Old database '$OLD_DB' preserved"
        fi
    fi
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              Database Migration Script                       ║"
    echo "║              webflowmaster → db-finanzas                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    check_postgres
    check_databases
    create_backup_dir
    backup_data
    create_target_database
    migrate_data
    test_new_database
    
    echo ""
    success "Database migration completed successfully!"
    echo ""
    echo -e "${BLUE}📋 Migration Summary:${NC}"
    echo "   • Source: $OLD_DB"
    echo "   • Target: $NEW_DB"
    echo "   • Backup: $BACKUP_DIR"
    echo "   • Host: $POSTGRES_HOST:$POSTGRES_PORT"
    echo ""
    echo -e "${YELLOW}📝 Next Steps:${NC}"
    echo "   1. Update your .env file (already done)"
    echo "   2. Test your application: npm run dev"
    echo "   3. Verify all data is accessible"
    echo ""
    
    cleanup_old_database
    
    echo -e "${GREEN}✅ Migration process complete!${NC}"
}

# Run the script
main "$@"
