#!/bin/bash

# WebFlowMaster Supabase Configuration Script
# Configures self-hosted Supabase instance for db-finanzas database

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SUPABASE_DIR="/home/<USER>/GithubProjects/supabase"
WEBFLOWMASTER_DIR="/home/<USER>/GithubProjects/WebFlowMaster"
DATABASE_NAME="db-finanzas"
POSTGRES_PORT="54322"
API_PORT="54321"
STUDIO_PORT="54323"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Supabase directory exists
check_supabase_directory() {
    log "Checking Supabase directory..."
    
    if [ ! -d "$SUPABASE_DIR" ]; then
        error "Supabase directory not found at $SUPABASE_DIR"
        echo ""
        echo "Please ensure you have a self-hosted Supabase instance at the specified location."
        echo "You can set it up by:"
        echo "1. git clone https://github.com/supabase/supabase"
        echo "2. cd supabase/docker"
        echo "3. cp .env.example .env"
        echo "4. docker-compose up -d"
        exit 1
    fi
    
    success "Supabase directory found"
    
    # List contents
    log "Supabase directory contents:"
    ls -la "$SUPABASE_DIR" | head -10
}

# Check if Supabase is running
check_supabase_status() {
    log "Checking Supabase status..."
    
    # Check if Supabase CLI is available
    if command -v supabase &> /dev/null; then
        log "Using Supabase CLI to check status..."
        cd "$SUPABASE_DIR"
        if supabase status &> /dev/null; then
            success "Supabase is running (CLI)"
            supabase status
            return 0
        else
            warning "Supabase not running via CLI"
        fi
    fi
    
    # Check Docker containers
    log "Checking Docker containers..."
    if docker ps | grep -q supabase; then
        success "Supabase Docker containers are running"
        docker ps | grep supabase
        return 0
    fi
    
    # Check ports
    log "Checking if Supabase ports are in use..."
    if netstat -tulpn 2>/dev/null | grep -q ":$POSTGRES_PORT "; then
        success "PostgreSQL port $POSTGRES_PORT is in use"
    else
        warning "PostgreSQL port $POSTGRES_PORT is not in use"
    fi
    
    if netstat -tulpn 2>/dev/null | grep -q ":$API_PORT "; then
        success "API port $API_PORT is in use"
    else
        warning "API port $API_PORT is not in use"
    fi
}

# Start Supabase if not running
start_supabase() {
    log "Attempting to start Supabase..."
    
    cd "$SUPABASE_DIR"
    
    # Try Supabase CLI first
    if command -v supabase &> /dev/null; then
        log "Starting Supabase with CLI..."
        if supabase start; then
            success "Supabase started successfully with CLI"
            return 0
        else
            warning "Failed to start with CLI, trying Docker Compose..."
        fi
    fi
    
    # Try Docker Compose
    if [ -f "docker-compose.yml" ]; then
        log "Starting Supabase with Docker Compose..."
        if docker-compose up -d; then
            success "Supabase started successfully with Docker Compose"
            sleep 10  # Wait for services to be ready
            return 0
        else
            error "Failed to start Supabase with Docker Compose"
        fi
    fi
    
    error "Could not start Supabase. Please start it manually."
    return 1
}

# Create db-finanzas database
create_database() {
    log "Creating db-finanzas database..."
    
    # Wait for PostgreSQL to be ready
    log "Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if pg_isready -h localhost -p $POSTGRES_PORT -U postgres &> /dev/null; then
            success "PostgreSQL is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            error "PostgreSQL is not responding after 30 seconds"
            return 1
        fi
        sleep 1
    done
    
    # Check if database already exists
    if psql -h localhost -p $POSTGRES_PORT -U postgres -lqt | cut -d \| -f 1 | grep -qw "$DATABASE_NAME"; then
        success "Database '$DATABASE_NAME' already exists"
        return 0
    fi
    
    # Create database
    log "Creating database '$DATABASE_NAME'..."
    if psql -h localhost -p $POSTGRES_PORT -U postgres -c "CREATE DATABASE \"$DATABASE_NAME\";" &> /dev/null; then
        success "Database '$DATABASE_NAME' created successfully"
    else
        error "Failed to create database '$DATABASE_NAME'"
        return 1
    fi
    
    # Grant permissions
    log "Granting permissions..."
    psql -h localhost -p $POSTGRES_PORT -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE \"$DATABASE_NAME\" TO postgres;" &> /dev/null
    success "Permissions granted"
}

# Get Supabase API keys
get_api_keys() {
    log "Retrieving Supabase API keys..."
    
    cd "$SUPABASE_DIR"
    
    # Try to get keys from Supabase CLI
    if command -v supabase &> /dev/null; then
        log "Getting keys from Supabase CLI..."
        if supabase status | grep -q "anon key"; then
            ANON_KEY=$(supabase status | grep "anon key" | awk '{print $NF}')
            SERVICE_KEY=$(supabase status | grep "service_role key" | awk '{print $NF}')
            
            if [ ! -z "$ANON_KEY" ] && [ ! -z "$SERVICE_KEY" ]; then
                success "API keys retrieved from CLI"
                echo "ANON_KEY: $ANON_KEY"
                echo "SERVICE_KEY: $SERVICE_KEY"
                return 0
            fi
        fi
    fi
    
    # Try to get keys from .env file
    if [ -f ".env" ]; then
        log "Checking .env file for API keys..."
        if grep -q "ANON_KEY" .env; then
            ANON_KEY=$(grep "ANON_KEY" .env | cut -d'=' -f2)
            SERVICE_KEY=$(grep "SERVICE_ROLE_KEY" .env | cut -d'=' -f2)
            success "API keys found in .env file"
            return 0
        fi
    fi
    
    # Use default development keys
    warning "Using default development API keys"
    ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
}

# Update WebFlowMaster .env file
update_webflowmaster_env() {
    log "Updating WebFlowMaster .env file..."
    
    cd "$WEBFLOWMASTER_DIR"
    
    # Backup current .env
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # Update API keys if we have them
    if [ ! -z "$ANON_KEY" ]; then
        sed -i "s/SUPABASE_ANON_KEY=.*/SUPABASE_ANON_KEY=$ANON_KEY/" .env
        success "Updated SUPABASE_ANON_KEY"
    fi
    
    if [ ! -z "$SERVICE_KEY" ]; then
        sed -i "s/SUPABASE_SERVICE_ROLE_KEY=.*/SUPABASE_SERVICE_ROLE_KEY=$SERVICE_KEY/" .env
        success "Updated SUPABASE_SERVICE_ROLE_KEY"
    fi
    
    success "WebFlowMaster .env file updated"
}

# Test connectivity
test_connectivity() {
    log "Testing connectivity..."
    
    cd "$WEBFLOWMASTER_DIR"
    
    # Test database connection
    log "Testing database connectivity..."
    if node test-connectivity.js; then
        success "Database connectivity test passed"
    else
        error "Database connectivity test failed"
        return 1
    fi
    
    # Test Supabase API
    log "Testing Supabase API..."
    if curl -s "http://localhost:$API_PORT/health" | grep -q "ok"; then
        success "Supabase API is responding"
    else
        warning "Supabase API test failed or not available"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║            WebFlowMaster Supabase Configuration             ║"
    echo "║                  Database: db-finanzas                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    check_supabase_directory
    check_supabase_status
    
    # Ask user if they want to start Supabase
    if ! netstat -tulpn 2>/dev/null | grep -q ":$POSTGRES_PORT "; then
        echo ""
        read -p "Supabase doesn't appear to be running. Would you like to start it? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            start_supabase
        else
            warning "Please start Supabase manually and run this script again"
            exit 1
        fi
    fi
    
    create_database
    get_api_keys
    update_webflowmaster_env
    test_connectivity
    
    echo ""
    success "Supabase configuration completed successfully!"
    echo ""
    echo -e "${BLUE}📋 Configuration Summary:${NC}"
    echo "   • Database: $DATABASE_NAME"
    echo "   • PostgreSQL: localhost:$POSTGRES_PORT"
    echo "   • Supabase API: http://localhost:$API_PORT"
    echo "   • Supabase Studio: http://localhost:$STUDIO_PORT"
    echo ""
    echo -e "${YELLOW}📝 Next Steps:${NC}"
    echo "   1. Run database migration: cd $WEBFLOWMASTER_DIR && npx drizzle-kit push"
    echo "   2. Test the application: npm run dev"
    echo "   3. Access Supabase Studio: http://localhost:$STUDIO_PORT"
}

# Run the script
main "$@"
