# WebFlowMaster - Financial Management & Payroll System

A comprehensive full-stack financial management application built with React, Express, and PostgreSQL. The application provides mission-based financial tracking, expense management, and complete payroll administration with multi-format import/export capabilities.

## 🚀 Project Overview

WebFlowMaster is designed to help organizations manage their financial operations through:

- **Mission-based Financial Tracking**: Organize finances into distinct periods/courses
- **Transaction Management**: Track savings and discounts with automatic balance calculations
- **Expense Management**: Detailed expense tracking with category-based organization
- **Payroll Administration**: Complete payroll system with employee management and multi-format data processing
- **Analytics Dashboard**: Real-time insights with charts and financial metrics

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Radix UI** components with shadcn/ui design system
- **Tailwind CSS** for styling
- **TanStack Query** for server state management
- **Wouter** for lightweight routing
- **React Hook Form** with Zod validation
- **Recharts** for data visualization

### Backend
- **Node.js** with Express.js
- **TypeScript** with ES modules
- **Drizzle ORM** with PostgreSQL
- **Neon Database** (serverless PostgreSQL)
- **Zod** for schema validation
- **Multer** for file uploads

### Build Tools
- **Vite** for frontend development and building
- **esbuild** for backend bundling
- **tsx** for TypeScript execution in development

## 📋 Prerequisites

Before setting up the project, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager
- **PostgreSQL** database (local or cloud-based)
- **Git** for version control

## 🔧 Installation Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/WebFlowMaster.git
cd WebFlowMaster
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Create a `.env` file in the root directory with the following variables:

```env
# Database Configuration
DATABASE_URL=postgresql://postgres:postgresql@localhost:5433/webflowmaster

# Application Environment
NODE_ENV=development
```

**Note**: Replace the database URL with your actual PostgreSQL connection string.

### 4. Database Setup

#### Option A: Local PostgreSQL Setup

1. **Install PostgreSQL** on your system
2. **Create a database** named `webflowmaster`
3. **Configure connection** using the provided credentials:
   - Host: `localhost`
   - Port: `5433`
   - User: `postgres`
   - Password: `postgresql`
   - Database: `webflowmaster`

#### Option B: Cloud Database (Recommended)

1. **Sign up** for a cloud PostgreSQL service (Neon, Supabase, etc.)
2. **Create a new database**
3. **Update** the `DATABASE_URL` in your `.env` file

### 5. Database Migration

Push the database schema to your PostgreSQL instance:

```bash
npm run db:push
```

### 6. Start the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:5000`

## 🚀 Usage Instructions

### Starting the Application

1. **Development mode**: `npm run dev`
2. **Production build**: `npm run build && npm start`
3. **Type checking**: `npm run check`

### Core Features

#### Financial Management
- **Dashboard**: View balance overview, charts, and recent activity
- **Transactions**: Add savings (+) and discounts (-) with automatic balance calculation
- **Expenses**: Track detailed expenses within categories funded by discounts
- **Analytics**: Historical balance data and financial insights

#### Payroll Management
- **Employee Management**: Add and manage employee information
- **Payroll Reports**: Create monthly payroll reports with exchange rates
- **File Import**: Support for CSV, Excel, JSON, XML, and Markdown formats
- **Data Export**: Export payroll data in multiple formats

### Navigation
- **Desktop**: Use the sidebar navigation
- **Mobile**: Use the bottom navigation bar and mobile header

## 📁 Project Structure

```
WebFlowMaster/
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Application pages/routes
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utility functions and configurations
│   │   └── index.css       # Global styles
│   └── index.html          # HTML entry point
├── server/                 # Backend Express application
│   ├── db.ts              # Database connection setup
│   ├── index.ts           # Server entry point
│   ├── routes.ts          # API route definitions
│   ├── storage.ts         # Database operations
│   ├── payroll-processor-v2.ts  # File processing logic
│   └── vite.ts            # Vite integration for development
├── shared/                 # Shared code between client and server
│   └── schema.ts          # Database schema and validation
├── package.json           # Dependencies and scripts
├── drizzle.config.ts      # Database ORM configuration
├── vite.config.ts         # Frontend build configuration
└── tsconfig.json          # TypeScript configuration
```

## 🤝 Contributing Guidelines

We welcome contributions to WebFlowMaster! Please follow these guidelines:

### Getting Started
1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/your-feature-name`
3. **Make** your changes with clear, descriptive commits
4. **Test** your changes thoroughly
5. **Submit** a pull request with a detailed description

### Code Standards
- Follow **TypeScript** best practices
- Use **ESLint** and **Prettier** for code formatting
- Write **meaningful commit messages**
- Add **tests** for new functionality
- Update **documentation** as needed

### Pull Request Process
1. Ensure all tests pass
2. Update README.md if needed
3. Request review from maintainers
4. Address feedback promptly

## 📄 License Information

This project is licensed under the **MIT License**. See the [LICENSE](LICENSE) file for details.

### MIT License Summary
- ✅ Commercial use allowed
- ✅ Modification allowed
- ✅ Distribution allowed
- ✅ Private use allowed
- ❌ No warranty provided
- ❌ No liability assumed

## 🆘 Support & Documentation

- **Issues**: Report bugs and request features via GitHub Issues
- **Documentation**: Additional docs available in the `/docs` folder
- **Community**: Join our discussions in GitHub Discussions

## 🔄 Recent Updates

- **July 02, 2025**: Complete payroll system refactoring with improved multi-format file processing
- **Enhanced Error Handling**: Better error messages and validation
- **CORS Support**: Full CORS implementation for API endpoints
- **File Processing**: Robust support for CSV, Excel, JSON, XML, and Markdown formats

---

**Built with ❤️ by the WebFlowMaster Team**
