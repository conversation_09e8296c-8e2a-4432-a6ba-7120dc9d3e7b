name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  # Code Quality and Linting
  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run TypeScript check
        run: npm run check

      - name: Run ESLint
        run: npm run lint
        continue-on-error: true

      - name: Run Prettier check
        run: npm run format:check
        continue-on-error: true

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Unit and Integration Tests
  test:
    name: Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgresql
          POSTGRES_USER: postgres
          POSTGRES_DB: webflowmaster_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test environment
        env:
          DATABASE_URL: postgresql://postgres:postgresql@localhost:5432/webflowmaster_test
          JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only-32-chars
          SESSION_SECRET: test-session-secret-key-for-testing-purposes-only-32
          NODE_ENV: test
        run: |
          npm run db:push

      - name: Run unit tests
        env:
          DATABASE_URL: postgresql://postgres:postgresql@localhost:5432/webflowmaster_test
          JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only-32-chars
          SESSION_SECRET: test-session-secret-key-for-testing-purposes-only-32
          NODE_ENV: test
        run: npm run test:unit

      - name: Run integration tests
        env:
          DATABASE_URL: postgresql://postgres:postgresql@localhost:5432/webflowmaster_test
          JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only-32-chars
          SESSION_SECRET: test-session-secret-key-for-testing-purposes-only-32
          NODE_ENV: test
        run: npm run test:integration

      - name: Generate test coverage
        env:
          DATABASE_URL: postgresql://postgres:postgresql@localhost:5432/webflowmaster_test
          JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only-32-chars
          SESSION_SECRET: test-session-secret-key-for-testing-purposes-only-32
          NODE_ENV: test
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # End-to-End Tests
  e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgresql
          POSTGRES_USER: postgres
          POSTGRES_DB: webflowmaster_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npm run playwright:install

      - name: Setup E2E environment
        env:
          DATABASE_URL: postgresql://postgres:postgresql@localhost:5432/webflowmaster_e2e
          JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only-32-chars
          SESSION_SECRET: test-session-secret-key-for-testing-purposes-only-32
          NODE_ENV: test
        run: |
          npm run db:push

      - name: Run E2E tests
        env:
          DATABASE_URL: postgresql://postgres:postgresql@localhost:5432/webflowmaster_e2e
          JWT_SECRET: test-jwt-secret-key-for-testing-purposes-only-32-chars
          SESSION_SECRET: test-session-secret-key-for-testing-purposes-only-32
          NODE_ENV: test
        run: npm run test:e2e

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

  # Build Application
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [lint, test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            client/dist/
          retention-days: 7

  # Dependency Check
  dependency-check:
    name: Dependency Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Check for outdated dependencies
        run: npm outdated || true

      - name: Check for security vulnerabilities
        run: npm audit --audit-level=high

      - name: License check
        run: npx license-checker --summary
        continue-on-error: true

  # Performance Tests
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true
