# Financial Missions Tracker

## Overview

This is a comprehensive full-stack financial management application built with React, Express, and PostgreSQL. The application allows users to track savings, discounts, and expenses across different "missions" (financial periods/courses), plus complete payroll report management with multi-format import/export capabilities. It provides a comprehensive dashboard for monitoring financial progress, managing transactions, detailed expense tracking, and payroll administration.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **UI Library**: Radix UI components with shadcn/ui design system
- **Styling**: Tailwind CSS with CSS variables for theming
- **State Management**: TanStack Query (React Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Form Handling**: React Hook Form with Zod validation
- **Charts**: Recharts for data visualization

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database ORM**: Drizzle ORM with PostgreSQL
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Validation**: Zod schemas shared between client and server
- **Build System**: Vite for frontend, esbuild for backend

### Database Design
The application uses a relational database structure with eight main entities:

**Financial Management:**
- **Missions**: Financial periods/courses with course numbers
- **Transactions**: Savings and discount entries with resulting balances
- **Expense Categories**: Categories funded by discount amounts (1ra, 2da, 3ra)
- **Expenses**: Individual expense items within categories

**Payroll Management:**
- **Payroll Reports**: Monthly payroll reports with year, month, and exchange rates
- **Employees**: Employee information including brigade, file number, CI, etc.
- **Payroll Concepts**: Salary concepts categorized as ABONOS or DESCUENTOS
- **Payroll Entries**: Individual payroll line items linking employees, concepts, and amounts

## Key Components

### Data Models
- **Missions**: Organize financial tracking into distinct periods
- **Transactions**: Track savings (+) and discounts (-) with cumulative balances
- **Expense Categories**: Break down discount amounts into spending categories
- **Expenses**: Detailed expense tracking within categories

### User Interface
- **Responsive Design**: Mobile-first approach with desktop sidebar navigation
- **Dashboard**: Overview with balance charts, key metrics, and recent activity
- **Transaction Management**: Add, view, and delete financial transactions
- **Expense Tracking**: Detailed expense management by category
- **Real-time Updates**: Optimistic updates with React Query

### API Structure
RESTful API endpoints for:

**Financial Management:**
- `/api/missions` - Mission management
- `/api/transactions` - Transaction CRUD operations
- `/api/expenses` - Expense management
- `/api/expense-categories` - Category management
- `/api/analytics` - Balance and historical data

**Payroll Management:**
- `/api/payroll-reports` - Payroll report CRUD operations
- `/api/employees` - Employee management
- `/api/payroll-concepts` - Payroll concept management
- `/api/payroll/import` - Multi-format file import (CSV, Excel, JSON, XML, Markdown)
- `/api/payroll/export/:reportId/:format` - Multi-format export functionality

## Data Flow

1. **Data Entry**: Users input transactions (savings/discounts) and expenses
2. **Balance Calculation**: System automatically calculates running balances
3. **Category Funding**: Discount transactions fund expense categories
4. **Analytics**: Historical data provides insights through charts and metrics
5. **Real-time Updates**: UI updates immediately with optimistic updates

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: Serverless PostgreSQL connection
- **drizzle-orm**: Type-safe database ORM
- **@tanstack/react-query**: Server state management
- **react-hook-form**: Form handling and validation
- **recharts**: Chart visualization library

### UI Dependencies
- **@radix-ui/***: Headless UI components
- **tailwindcss**: Utility-first CSS framework
- **class-variance-authority**: Component variant management
- **lucide-react**: Icon library

### Development Dependencies
- **vite**: Frontend build tool
- **tsx**: TypeScript execution for development
- **esbuild**: Backend bundling for production

## Deployment Strategy

### Development Environment
- **Frontend**: Vite dev server with HMR
- **Backend**: Express server with tsx for TypeScript execution
- **Database**: Neon Database with connection pooling

### Production Build
- **Frontend**: Static files built with Vite
- **Backend**: Bundled with esbuild for Node.js deployment
- **Database**: Production Neon Database instance

### Environment Configuration
- `DATABASE_URL`: PostgreSQL connection string
- `NODE_ENV`: Environment mode (development/production)

## User Preferences

Preferred communication style: Simple, everyday language.

## Recent Changes

- **July 02, 2025**: Refactorización completa del sistema de importación de nómina
  - Creado PayrollProcessorV2 con manejo robusto de múltiples formatos
  - Solucionado error "Papa.parse is not a function" con importaciones correctas
  - Mejorado manejo de errores con mensajes específicos en español
  - Implementado XMLHttpRequest para uploads más estables
  - Sistema probado y funcionando con archivos Markdown, JSON, CSV, Excel y XML
  - Agregado soporte CORS completo al servidor

## Changelog

- July 02, 2025. Initial setup and complete payroll system implementation