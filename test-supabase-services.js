#!/usr/bin/env node

/**
 * Supabase Services Test Script
 * Tests individual Supabase services to identify what's running
 */

import { readFileSync } from 'fs';

// Load environment variables
const envContent = readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});
Object.assign(process.env, envVars);

console.log('🔍 Testing Supabase Services Availability');
console.log('=' .repeat(50));

async function testServices() {
  const services = [
    { name: 'API Gateway', port: 54321, path: '/health' },
    { name: 'PostgreSQL', port: 54322, type: 'database' },
    { name: 'Supabase Studio', port: 54323, path: '/' },
    { name: 'Inbucket (Email)', port: 54324, path: '/' },
    { name: 'Edge Functions', port: 54325, path: '/' }
  ];

  console.log('\n📡 Testing HTTP Services...');
  
  for (const service of services) {
    if (service.type === 'database') {
      console.log(`⏭️  Skipping ${service.name} (port ${service.port}) - Database service`);
      continue;
    }

    try {
      const url = `http://localhost:${service.port}${service.path}`;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);
      
      const response = await fetch(url, { 
        signal: controller.signal,
        method: 'GET'
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        console.log(`✅ ${service.name} (port ${service.port}) - RUNNING`);
      } else {
        console.log(`⚠️  ${service.name} (port ${service.port}) - Responded with status ${response.status}`);
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`❌ ${service.name} (port ${service.port}) - TIMEOUT`);
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${service.name} (port ${service.port}) - NOT RUNNING`);
      } else {
        console.log(`❌ ${service.name} (port ${service.port}) - ERROR: ${error.message}`);
      }
    }
  }

  console.log('\n🗄️  Testing PostgreSQL Database...');
  
  // Test PostgreSQL using a simple connection attempt
  try {
    const { spawn } = await import('child_process');
    
    const testConnection = () => {
      return new Promise((resolve, reject) => {
        const pg_isready = spawn('pg_isready', [
          '-h', 'localhost',
          '-p', '54322',
          '-U', 'postgres'
        ]);
        
        let output = '';
        pg_isready.stdout.on('data', (data) => {
          output += data.toString();
        });
        
        pg_isready.stderr.on('data', (data) => {
          output += data.toString();
        });
        
        pg_isready.on('close', (code) => {
          if (code === 0) {
            resolve('PostgreSQL is accepting connections');
          } else {
            reject(new Error(`pg_isready failed: ${output}`));
          }
        });
        
        // Timeout after 5 seconds
        setTimeout(() => {
          pg_isready.kill();
          reject(new Error('Connection test timeout'));
        }, 5000);
      });
    };
    
    const result = await testConnection();
    console.log(`✅ PostgreSQL (port 54322) - ${result}`);
    
  } catch (error) {
    console.log(`❌ PostgreSQL (port 54322) - ${error.message}`);
  }

  console.log('\n🔑 Testing API Keys...');
  
  // Test API keys with a simple request
  const supabaseUrl = process.env.SUPABASE_URL;
  const anonKey = process.env.SUPABASE_ANON_KEY;
  
  if (supabaseUrl && anonKey) {
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        headers: {
          'apikey': anonKey,
          'Authorization': `Bearer ${anonKey}`
        }
      });
      
      if (response.ok) {
        console.log('✅ API Keys - Valid and working');
      } else {
        console.log(`⚠️  API Keys - Response status: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ API Keys - Test failed: ${error.message}`);
    }
  } else {
    console.log('⚠️  API Keys - Not configured');
  }

  console.log('\n📊 Summary');
  console.log('=' .repeat(50));
  
  // Check if any services are running
  const runningServices = [];
  const notRunningServices = [];
  
  // We'll need to re-check services for summary
  for (const service of services) {
    if (service.type === 'database') continue;
    
    try {
      const url = `http://localhost:${service.port}${service.path}`;
      const response = await fetch(url, { 
        method: 'GET',
        signal: AbortSignal.timeout(2000)
      });
      
      if (response.ok) {
        runningServices.push(service.name);
      } else {
        notRunningServices.push(service.name);
      }
    } catch (error) {
      notRunningServices.push(service.name);
    }
  }
  
  if (runningServices.length > 0) {
    console.log(`✅ Running services: ${runningServices.join(', ')}`);
  }
  
  if (notRunningServices.length > 0) {
    console.log(`❌ Not running: ${notRunningServices.join(', ')}`);
  }
  
  console.log('\n💡 Recommendations:');
  
  if (notRunningServices.length === services.length - 1) { // -1 for database
    console.log('   • Supabase appears to be completely stopped');
    console.log('   • Start Supabase with: cd /home/<USER>/GithubProjects/supabase && supabase start');
    console.log('   • Or with Docker: cd /home/<USER>/GithubProjects/supabase/docker && docker-compose up -d');
  } else if (notRunningServices.includes('API Gateway')) {
    console.log('   • API Gateway is not running - this is critical for database operations');
    console.log('   • Check Supabase logs for errors');
  } else if (runningServices.includes('API Gateway')) {
    console.log('   • API Gateway is running - try testing database connection');
    console.log('   • Run: node test-connectivity.js');
  }
  
  console.log('\n🔗 Useful URLs (if services are running):');
  console.log('   • Supabase Studio: http://localhost:54323');
  console.log('   • API Gateway: http://localhost:54321');
  console.log('   • Email Testing: http://localhost:54324');
}

testServices().catch(console.error);
