import { pgTable, text, serial, integer, real, timestamp } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const missions = pgTable("missions", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  courseNumber: integer("course_number").notNull(),
});

export const transactions = pgTable("transactions", {
  id: serial("id").primaryKey(),
  date: text("date").notNull(), // Format: 'YYYY-MM'
  type: text("type").notNull(), // 'savings' or 'discount'
  amount: real("amount").notNull(),
  resultingBalance: real("resulting_balance").notNull(),
  missionId: integer("mission_id").references(() => missions.id),
  createdAt: timestamp("created_at").defaultNow(),
});

export const expenseCategories = pgTable("expense_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(), // '1ra', '2da', '3ra'
  fundingAmount: real("funding_amount").notNull(), // The discount amount that funds this category
  missionId: integer("mission_id").references(() => missions.id),
});

export const expenses = pgTable("expenses", {
  id: serial("id").primaryKey(),
  description: text("description").notNull(),
  amount: real("amount").notNull(),
  categoryId: integer("category_id").references(() => expenseCategories.id),
  createdAt: timestamp("created_at").defaultNow(),
});

// Payroll tables
export const payrollReports = pgTable("payroll_reports", {
  id: serial("id").primaryKey(),
  year: integer("year").notNull(),
  month: integer("month").notNull(),
  exchangeRate: real("exchange_rate").notNull().default(931.00),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const employees = pgTable("employees", {
  id: serial("id").primaryKey(),
  brigade: text("brigade").notNull(),
  fileNumber: text("file_number").notNull(), // Expediente
  name: text("name").notNull(),
  ci: text("ci").notNull(), // Cédula de Identidad
  signature: text("signature"), // Firma
  costCenter: text("cost_center").notNull(),
  organizationalUnit: text("organizational_unit").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const payrollConcepts = pgTable("payroll_concepts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  section: text("section").notNull(), // 'ABONOS' or 'DESCUENTOS'
  isActive: integer("is_active").notNull().default(1), // boolean as integer
  displayOrder: integer("display_order").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow(),
});

export const payrollEntries = pgTable("payroll_entries", {
  id: serial("id").primaryKey(),
  reportId: integer("report_id").references(() => payrollReports.id),
  employeeId: integer("employee_id").references(() => employees.id),
  conceptId: integer("concept_id").references(() => payrollConcepts.id),
  amountKz: real("amount_kz").notNull().default(0),
  amountUsd: real("amount_usd").notNull().default(0),
  exchangeRate: real("exchange_rate").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const missionsRelations = relations(missions, ({ many }) => ({
  transactions: many(transactions),
  expenseCategories: many(expenseCategories),
}));

export const transactionsRelations = relations(transactions, ({ one }) => ({
  mission: one(missions, {
    fields: [transactions.missionId],
    references: [missions.id],
  }),
}));

export const expenseCategoriesRelations = relations(expenseCategories, ({ one, many }) => ({
  mission: one(missions, {
    fields: [expenseCategories.missionId],
    references: [missions.id],
  }),
  expenses: many(expenses),
}));

export const expensesRelations = relations(expenses, ({ one }) => ({
  category: one(expenseCategories, {
    fields: [expenses.categoryId],
    references: [expenseCategories.id],
  }),
}));

// Payroll relations
export const payrollReportsRelations = relations(payrollReports, ({ many }) => ({
  entries: many(payrollEntries),
}));

export const employeesRelations = relations(employees, ({ many }) => ({
  payrollEntries: many(payrollEntries),
}));

export const payrollConceptsRelations = relations(payrollConcepts, ({ many }) => ({
  payrollEntries: many(payrollEntries),
}));

export const payrollEntriesRelations = relations(payrollEntries, ({ one }) => ({
  report: one(payrollReports, {
    fields: [payrollEntries.reportId],
    references: [payrollReports.id],
  }),
  employee: one(employees, {
    fields: [payrollEntries.employeeId],
    references: [employees.id],
  }),
  concept: one(payrollConcepts, {
    fields: [payrollEntries.conceptId],
    references: [payrollConcepts.id],
  }),
}));

// Insert schemas
export const insertMissionSchema = createInsertSchema(missions).omit({
  id: true,
});

export const insertTransactionSchema = createInsertSchema(transactions).omit({
  id: true,
  createdAt: true,
});

export const insertExpenseCategorySchema = createInsertSchema(expenseCategories).omit({
  id: true,
});

export const insertExpenseSchema = createInsertSchema(expenses).omit({
  id: true,
  createdAt: true,
});

// Payroll insert schemas
export const insertPayrollReportSchema = createInsertSchema(payrollReports).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertEmployeeSchema = createInsertSchema(employees).omit({
  id: true,
  createdAt: true,
});

export const insertPayrollConceptSchema = createInsertSchema(payrollConcepts).omit({
  id: true,
  createdAt: true,
});

export const insertPayrollEntrySchema = createInsertSchema(payrollEntries).omit({
  id: true,
  createdAt: true,
});

// Types
export type Mission = typeof missions.$inferSelect;
export type InsertMission = z.infer<typeof insertMissionSchema>;

export type Transaction = typeof transactions.$inferSelect;
export type InsertTransaction = z.infer<typeof insertTransactionSchema>;

export type ExpenseCategory = typeof expenseCategories.$inferSelect;
export type InsertExpenseCategory = z.infer<typeof insertExpenseCategorySchema>;

export type Expense = typeof expenses.$inferSelect;
export type InsertExpense = z.infer<typeof insertExpenseSchema>;

// Extended types with relations
export type MissionWithRelations = Mission & {
  transactions: Transaction[];
  expenseCategories: (ExpenseCategory & {
    expenses: Expense[];
  })[];
};

export type TransactionWithMission = Transaction & {
  mission: Mission | null;
};

export type ExpenseWithCategory = Expense & {
  category: ExpenseCategory | null;
};

// Payroll types
export type PayrollReport = typeof payrollReports.$inferSelect;
export type InsertPayrollReport = z.infer<typeof insertPayrollReportSchema>;

export type Employee = typeof employees.$inferSelect;
export type InsertEmployee = z.infer<typeof insertEmployeeSchema>;

export type PayrollConcept = typeof payrollConcepts.$inferSelect;
export type InsertPayrollConcept = z.infer<typeof insertPayrollConceptSchema>;

export type PayrollEntry = typeof payrollEntries.$inferSelect;
export type InsertPayrollEntry = z.infer<typeof insertPayrollEntrySchema>;

// Extended payroll types with relations
export type PayrollReportWithEntries = PayrollReport & {
  entries: (PayrollEntry & {
    employee: Employee;
    concept: PayrollConcept;
  })[];
};

export type PayrollEntryWithRelations = PayrollEntry & {
  employee: Employee | null;
  concept: PayrollConcept | null;
  report: PayrollReport | null;
};
